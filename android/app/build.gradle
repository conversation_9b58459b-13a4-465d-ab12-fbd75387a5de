plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.huawei.agconnect"
    id "com.hihonor.mcs.asplugin"
    id "kotlin-kapt"
    id "org.jetbrains.kotlin.kapt"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.xtj.person"
    compileSdkVersion 34 // 更新 compileSdkVersion
    ndkVersion "27.0.12077973"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lint {
        baseline = file("lint-baseline.xml")
    }

     signingConfigs {
         release {
             storeFile file('xtjr.keystore') // 确保文件路径正确
             storePassword 'XTJSK2024'
             keyAlias 'xtjrAlias'
             keyPassword 'XTJSK2024'
         }
         debug {
             storeFile file('xtjr.keystore') // 确保文件路径正确
             storePassword 'XTJSK2024'
             keyAlias 'xtjrAlias'
             keyPassword 'XTJSK2024'
         }
     }


    defaultConfig {
        applicationId "com.xtj.person"
        minSdkVersion 26
        targetSdkVersion 34 // 更新 targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        manifestPlaceholders = [
                "VIVO_APPKEY" : "43f58fd05a91b186c51fc5529ae1e4c1",
                "VIVO_APPID" : "105708680",
                "HONOR_APPID" : "104425729"
        ]

        ndk {
            abiFilters  'arm64-v8a'
        }

        externalNativeBuild {
            cmake {
                // 启用 OpenCV，并指向你放在 android 下的 SDK 目录
                arguments(
                        "-DWITH_OPENCV=ON",
                        // 注意：project.rootDir 已经是 android/ 目录，无需再拼一个 android
                        "-DOPENCV_SDK_DIR=${project.rootDir}/OpenCV-android-sdk",
                        "-DANDROID_STL=c++_shared"
                )
            }
        }




        applicationVariants.all { variant ->
            variant.outputs.all {
                outputFileName = "com.xtj.person_v${flutterVersionName}.apk"
            }

        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            signingConfig signingConfigs.release
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    packagingOptions {
        dex {
            useLegacyPackaging true
        }
        jniLibs {
            useLegacyPackaging true
        }
    }

    buildFeatures {
        viewBinding true
        dataBinding true
    }


    externalNativeBuild {
        cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version '3.22.1'
        }
    }
}


flutter {
    source '../..'
}

kapt {
    arguments {
        arg("rxhttp_rxjava", "3.1.5") //可传入rxjava2、rxjava3或具体版本号，如 3.1.1
//            arg("rxhttp_package", "rxhttp") //设置RxHttp相关类的包名，多module依赖时，需要配置不同的包名
    }
}


dependencies {
    implementation 'androidx.multidex:multidex:2.0.1'

    // Correct the versions to match the actual files in your libs folder
//    implementation(name: "auth_number_product-2.13.14-release", ext:'aar')  // Update to match your aar file
//    implementation(name: "logger-2.2.2-release", ext:'aar')
//    implementation(name: "main-2.2.3-release", ext:'aar')
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation 'androidx.appcompat:appcompat:1.2.0' //1.3.1



    // Huawei
    implementation 'com.tencent.timpush:huawei:8.3.6498'
    // XiaoMi
    implementation 'com.tencent.timpush:xiaomi:8.3.6498'
    // OPPO
    implementation 'com.tencent.timpush:oppo:8.3.6498'
    // vivo
    implementation 'com.tencent.timpush:vivo:8.3.6498'
    // Honor
    implementation 'com.tencent.timpush:honor:8.3.6498'
    // Meizu
//    implementation 'com.tencent.timpush:meizu:VERSION'
    // Google Firebase Cloud Messaging (Google FCM)
//    implementation 'com.tencent.timpush:fcm:VERSION'



    // CameraX
    implementation 'androidx.camera:camera-core:1.3.4'
    implementation 'androidx.camera:camera-camera2:1.3.4'
    implementation 'androidx.camera:camera-lifecycle:1.3.4'
    implementation 'androidx.camera:camera-view:1.3.4'
    implementation "com.google.guava:guava:31.0.1-android"

    // Data Binding
    implementation 'androidx.databinding:databinding-runtime:8.3.2'
    implementation 'androidx.databinding:viewbinding:8.3.2'

    // Architecture Components
    implementation 'com.kunminx.archi:unpeek-livedata:4.5.0-beta1'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-runtime:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-service:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-process:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-reactivestreams:2.7.0'

    // Networking
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.github.liujingxing.rxhttp:rxhttp:3.2.6'
    kapt 'com.github.liujingxing.rxhttp:rxhttp-compiler:3.2.6'

    // RxJava
    implementation 'io.reactivex.rxjava3:rxjava:3.1.5'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.1'
    implementation 'com.github.liujingxing.rxlife:rxlife-rxjava3:2.2.2'
    implementation 'com.github.liujingxing.rxlife:rxlife-coroutine:2.2.0'

    // UI Components
    implementation 'io.github.scwang90:refresh-layout-kernel:2.1.0'
    implementation 'io.github.scwang90:refresh-header-classics:2.1.0'
    implementation 'io.github.scwang90:refresh-header-two-level:2.1.0'
    implementation 'io.coil-kt:coil:2.4.0'
    implementation 'io.coil-kt:coil-gif:2.4.0'
    implementation 'com.geyifeng.immersionbar:immersionbar:3.2.2'
    implementation 'com.geyifeng.immersionbar:immersionbar-ktx:3.2.2'

    // Utilities
    implementation 'com.github.DSAppTeam:Anchors:v1.1.8'
    implementation 'com.blankj:utilcodex:1.30.6'
    implementation 'com.kingja.loadsir:loadsir:1.3.8'
    implementation 'com.tencent:mmkv:1.3.9'


    implementation 'com.github.gzu-liyujiang.AndroidPicker:Common:4.1.14'
    implementation 'com.github.gzu-liyujiang.AndroidPicker:CalendarPicker:4.1.14'
    implementation 'org.kodein.di:kodein-di:7.5.0'
    implementation 'org.kodein.di:kodein-di-framework-android-core:7.5.0'
    implementation 'org.kodein.di:kodein-di-framework-android-x:7.5.0'



    implementation("com.github.bumptech.glide:glide:4.12.0")
    implementation("com.github.bumptech.glide:compiler:4.12.0")

    implementation("com.github.lihangleo2:ShadowLayout:3.4.0")

    implementation("com.github.yalantis:ucrop:2.2.4-native")


    implementation("top.zibin:Luban:1.1.8")


    implementation("androidx.fragment:fragment-ktx:1.6.2")

    implementation ("com.github.luben:zstd-jni:1.5.2-3@aar")
    testImplementation("com.github.luben:zstd-jni:1.5.2-3")

    implementation 'com.google.android.material:material:1.9.0'

}


