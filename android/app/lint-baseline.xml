<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 7.3.0" type="baseline" client="gradle" dependencies="false" name="AGP (7.3.0)" variant="fatal" version="7.3.0">

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                checkListBeanLiveData.value = DataRepository.getCheckList(curriculumId,date).await()"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/CheckListViewModel.kt"
            line="16"
            column="47"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                selectCourseListLiveData.value = DataRepository.getSelectCourseList(keyword,size,page,date).await()"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/FacialCheckInTodayCourseViewModel.kt"
            line="21"
            column="50"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                queryUserInfoByPhoneBeanLiveData.value = DataRepository.queryUserInfoByPhone(phoneNum).await()"
        errorLine2="                                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/QueryUserByPhoneViewModel.kt"
            line="15"
            column="58"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                selectCourseListLiveData.value = DataRepository.getSelectCourseList(keyword,size,page,date).await()"
        errorLine2="                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/SelectCourseDialogViewModel.kt"
            line="21"
            column="50"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="              schoolListBeanLiveData.value = DataRepository.getSchoolList(page,size,keyword).await()"
        errorLine2="                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/SelectLocationDialogViewModel.kt"
            line="15"
            column="46"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                newestCheckInResultLiveData.value = DataRepository.getNewestCheckInResult(curriculumId,period).await()"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/StartCheckViewModel.kt"
            line="17"
            column="53"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                changeUserInfoResultLiveData.value = DataRepository.changeUserInfo(guid,userName,idNumber,phoneNum,&quot;&quot;).await()"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/StudentInfoEditViewModel.kt"
            line="17"
            column="54"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                checkRecordsBeanLiveDate.value = DataRepository.getClockInRecords(page,size,curriculumId,period,type).await()"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/ClockInResultFragmentViewModel.kt"
            line="17"
            column="46"/>
    </issue>

    <issue
        id="NullSafeMutableLiveData"
        message="Expected non-nullable value"
        errorLine1="                checkInfoFaceBeanLiveData.value = DataRepository.getCheckInfoFace(curriculumId).await()"
        errorLine2="                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/kotlin/com/xtj/person/common/viewmodel/CheckListViewModel.kt"
            line="17"
            column="50"/>
    </issue>

</issues>
