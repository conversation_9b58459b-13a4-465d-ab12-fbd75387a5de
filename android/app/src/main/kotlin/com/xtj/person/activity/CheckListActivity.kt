package com.xtj.person.activity

import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.postDelayed
import androidx.recyclerview.widget.LinearLayoutManager
import com.blankj.utilcode.util.ToastUtils
import com.github.gzuliyujiang.calendarpicker.CalendarPicker
import com.github.gzuliyujiang.calendarpicker.OnSingleDatePickListener
import com.xtj.person.activity.FacialCheckInTodayCourseActivity
import com.xtj.person.adapter.RvCheckListAdapter
import com.xtj.person.common.base.eventViewModel
import com.xtj.person.common.bean.CheckListBean
import com.xtj.person.common.bean.CheckListItem
import com.xtj.person.common.bean.CourseItem
import com.xtj.person.common.ext.getSelectSchoolItemBean
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.toJsonStr
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.DateUtils
import com.xtj.person.common.util.OnRvItemClickListener
import com.xtj.person.common.viewmodel.CheckListViewModel
import com.xtj.person.databinding.ActivityCheckListLayoutBinding
import com.xtj.person.dialog.SelectCourseDialogFragment
import com.xtj.person.dialog.SelectCourseDialogFragment.OnSelectCourseListener
import java.util.Date
import kotlin.code

class CheckListActivity: BaseVmActivity<CheckListViewModel, ActivityCheckListLayoutBinding>(), View.OnClickListener {
    var curriculumId:Int = -1
    var curriculumName:String = ""
    var selectDate:String =""
    var rvCheckListAdapter: RvCheckListAdapter?=null
    var checkListBeans : ArrayList<CheckListItem> = arrayListOf()

    override fun getViewBinding(inflater: LayoutInflater): ActivityCheckListLayoutBinding {

        return ActivityCheckListLayoutBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {

        curriculumId = intent.getIntExtra("curriculumId",-1)
        curriculumName = intent.getStringExtra("curriculumName")?:""

        subBinding.run {
            tvSelectCourse.text = curriculumName
            include.title.text = "人脸签到"
            include.backBtn.setOnClickListener { finish() }
            include.rightBtn.text="人脸录入"
            include.rightBtn.setOnClickListener {
                var intent = Intent(this@CheckListActivity,QueryUserByPhoneActivity::class.java)
                startActivity(intent)
            }
            tvDate.setOnClickListener(this@CheckListActivity)
            ivDate.setOnClickListener(this@CheckListActivity)
            tvDate.text = DateUtils.getTodayDateStr()
            selectDate = DateUtils.getTodayDateStr()
            rvCheckList.layoutManager = LinearLayoutManager(this@CheckListActivity)
            rvCheckListAdapter = RvCheckListAdapter(checkListBeans)
            rvCheckList.adapter = rvCheckListAdapter
            rvCheckListAdapter?.onItemClickListener = object:OnRvItemClickListener{
                override fun onClickItem(position: Int) {
                                        var intent = Intent(this@CheckListActivity, StartCheckActivity::class.java)
                    intent.putExtra("curriculumName",checkListBeans[position].curriculum_name)
                    intent.putExtra("curriculumId",curriculumId)
                    intent.putExtra("period",checkListBeans[position].period)
                    intent.putExtra("timeTitle",checkListBeans[position].title)
                    startActivity(intent)

                }

            }
            tvSelectCourse.setOnClickListener(this@CheckListActivity)

//
//            tvItemStartCheckIn.setOnClickListener(this@CheckListActivity)
        }
        showHideFaceDownloadProgress(33)
        mViewModel.getCheckList(curriculumId,selectDate)
        mViewModel.getCheckInfoFace(curriculumId)

    }

    private fun showHideFaceDownloadProgress(percent:Int){
        subBinding.run {
            if(percent<0){
                faceDownloadProgress.progress=0
                tvFaceProgress.text = "获取人脸信息失败"
            } else if(percent==100){
                faceDownloadProgress.progress=percent
                tvFaceProgress.text = "获取人脸信息成功"
                llFaceProgress.postDelayed (1000){
                    llFaceProgress.gone()
                }
            }else{
                faceDownloadProgress.progress=percent
                llFaceProgress.visible()
                tvFaceProgress.text = "获取人脸信息${percent}%"
            }
        }

    }


    override fun onClick(v: View?) {

        subBinding.run {
            when(v){

                tvSelectCourse->{
                    val schoolId =  getSelectSchoolItemBean()?.id?:-1
                    if(schoolId==-1){
                        ToastUtils.showShort("请先选择校区")
                        return
                    }
                    val selectCourseDialogFragment = SelectCourseDialogFragment.newInstance(selectDate,false)
                    selectCourseDialogFragment.show(supportFragmentManager,"")

                    selectCourseDialogFragment.onSelectCourseListener = object:OnSelectCourseListener{
                        override fun selectCourse(courseItem: CourseItem) {
                            curriculumId = courseItem.curriculum_id
                            curriculumName = courseItem.curriculum_name
                            tvSelectCourse.text = curriculumName

                            mViewModel.getCheckList(curriculumId,selectDate)

                        }

                        override fun selectAll() {
                            tvSelectCourse.text = "全部课程"
                        }

                    }


                }


                tvDate,ivDate->{
                    val picker = CalendarPicker(this@CheckListActivity)
//                    picker.setRangeDateOnFuture(3)
                    picker.enablePagerSnap()

                    val  singleTimeInMillis = System.currentTimeMillis()

                    picker.setSelectedDate(singleTimeInMillis)
                    picker.setOnSingleDatePickListener(object : OnSingleDatePickListener {


                        override fun onSingleDatePicked(date: Date) {
                            tvDate.text =  DateUtils.getDateStr(date)
                            selectDate =  DateUtils.getDefaultFormatDate(date)
                           mViewModel.getCheckList(curriculumId,selectDate)

                        }

                        override fun onMonthChanged(date: Date) {

                        }
                    })
                    picker.show()
                }

//                tvItemStartCheckIn->{
//                    var intent = Intent(this@CheckListActivity, StartCheckActivity::class.java)
//                    startActivity(intent)
//                }
            }
        }
    }

    override fun initObserver() {
        super.initObserver()

        mViewModel.checkListBeanLiveData.observe(this@CheckListActivity){
            checkListBeans.clear()
            if(it.code==200){
                if(!it.data.List.isNullOrEmpty()){
                    checkListBeans.addAll(it.data.List)
                    rvCheckListAdapter?.notifyDataSetChanged()
                }
            }


        }

        mViewModel.checkInfoFaceBeanLiveData.observe(this){
            if(it.code==0){
                if(!it.data.isNullOrEmpty()){
                    showHideFaceDownloadProgress(66)
                    eventViewModel.serverFaceDataList.clear()
                    eventViewModel.serverFaceDataList.addAll(it.data)
                    showHideFaceDownloadProgress(100)
                }else{
                    showHideFaceDownloadProgress(-1)
                }

            }else{
                showHideFaceDownloadProgress(-1)
            }

        }

        mViewModel.checkInfoFaceErrorLiveData.observe(this){
            showHideFaceDownloadProgress(-1)

        }
    }


}