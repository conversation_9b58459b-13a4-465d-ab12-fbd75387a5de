package com.xtj.person.activity

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.util.Rational
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.core.UseCaseGroup
import androidx.camera.core.ViewPort
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.R
import com.xtj.person.common.ext.dismissLoadingExt
import com.xtj.person.common.ext.getColorExt
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.isVisible
import com.xtj.person.common.ext.showLoadingExt
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.CameraUtils.chooseAspectRatio
import com.xtj.person.common.util.CameraUtils.transformCoordinates
import com.xtj.person.common.util.CameraUtils.yuv420888ToNV21
import com.xtj.person.common.util.ImageViewUtils
import com.xtj.person.common.util.ImageViewUtils.copyExif
import com.xtj.person.common.util.JniUtils.detectNV21
import com.xtj.person.common.viewmodel.StudentInfoEditViewModel
import com.xtj.person.databinding.ActivityLayoutTakePictureBinding
import top.zibin.luban.CompressionPredicate
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.Executors

class TakePictureActivity: BaseVmActivity<StudentInfoEditViewModel, ActivityLayoutTakePictureBinding>(),
    View.OnClickListener {

    data class Entry(val name: String, val feat: FloatArray)
    private val db = mutableListOf<Entry>()
    private var lastNV21: ByteArray? = null
    private var frameW: Int = 0
    private var frameH: Int = 0
    private var frameRotation: Int = 0
    private val ENABLE_RECOG = true // 启用识别功能

    private var cameraProvider: ProcessCameraProvider? = null
    private var cameraExecutor = Executors.newSingleThreadExecutor()
    private var camera: Camera? = null
    private var cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA

    private  val TAG = "CameraXApp"
    private  val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"
    private var resultImagePath:String = ""

    private var imageCapture: ImageCapture? = null

    private var isShuttable = false
    private var isFrontCamera: Boolean = true
    @Volatile private var cachedScaleType: PreviewView.ScaleType = PreviewView.ScaleType.FILL_START
    @Volatile private var viewW: Int = 0
    @Volatile private var viewH: Int = 0

    override fun getViewBinding(inflater: LayoutInflater): ActivityLayoutTakePictureBinding {
        return ActivityLayoutTakePictureBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {
        subBinding.run {
            include.title.text = "人脸采集"
            include.backBtn.setOnClickListener{
                finish()
            }

            cachedScaleType = preview.scaleType
            preview.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
                viewW = v.width
                viewH = v.height
            }

            ivStartShutter.setOnClickListener(this@TakePictureActivity)
            ivSwitchCamera.setOnClickListener(this@TakePictureActivity)
            llTakePictureAgain.setOnClickListener(this@TakePictureActivity)
            llCommint.setOnClickListener(this@TakePictureActivity)
        }
        ensurePermissionsAndStart()
    }


    private fun ensurePermissionsAndStart() {
        val perms = arrayOf(Manifest.permission.CAMERA,Manifest.permission.WRITE_EXTERNAL_STORAGE)
        val need = perms.any { ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED }
        if (need) ActivityCompat.requestPermissions(this, perms, 100)
        bindCamera()
    }

    private fun bindCamera() {
        val providerFuture = ProcessCameraProvider.getInstance(this)
        providerFuture.addListener({
             cameraProvider = providerFuture.get()
            // Choose aspect ratio closest to screen for larger FOV
            val dm = resources.displayMetrics
            val screenW = dm.widthPixels
            val screenH = dm.heightPixels
            val ratio = chooseAspectRatio(screenW, screenH)

            val preview = Preview.Builder()
                .setTargetAspectRatio(ratio)
                .setTargetRotation(subBinding.preview.display.rotation)
                .build()
            preview.setSurfaceProvider(subBinding.preview.surfaceProvider)
            // Fill the view to mimic native camera preview experience
            subBinding.preview.scaleType = PreviewView.ScaleType.FILL_CENTER
            // Update cached scale type after setting
            cachedScaleType = subBinding.preview.scaleType

            val analyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setTargetAspectRatio(ratio)
                .setTargetRotation(subBinding.preview.display.rotation)
                .build()
            cameraExecutor = Executors.newSingleThreadExecutor()
            analyzer.setAnalyzer(cameraExecutor) { image ->
                analyzeFrame(image)
                image.close()
            }


            isFrontCamera = (cameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA)
            try {
                cameraProvider?.unbindAll()
                // Use a shared ViewPort so preview and analysis have consistent crop/FOV
                val vpW = if (viewW > 0) viewW else screenW
                val vpH = if (viewH > 0) viewH else screenH
                val rotation = subBinding.preview.display.rotation
                val viewPort = ViewPort.Builder(Rational(vpW, vpH), rotation)
                    .setScaleType(ViewPort.FILL_CENTER)
                    .setLayoutDirection(subBinding.preview.layoutDirection)
                    .build()
                val group = UseCaseGroup.Builder()
                    .addUseCase(preview)
                    .addUseCase(analyzer)
                    .addUseCase(ImageCapture.Builder()
                        .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                        .build()
                        .also { imageCapture = it })
                    .setViewPort(viewPort)
                    .build()
                camera = cameraProvider?.bindToLifecycle(this as LifecycleOwner, cameraSelector, group)
            } catch (_: Exception) {}
        }, ContextCompat.getMainExecutor(this))
    }


    private fun analyzeFrame(image: ImageProxy) {
        val nv21 = yuv420888ToNV21(image)
        frameW = image.width
        frameH = image.height
        frameRotation = image.imageInfo.rotationDegrees
        lastNV21 = nv21

        // Debug: Log actual dimensions (use cached view size; don't touch View off main thread)
        val previewW = viewW
        val previewH = viewH
        Log.d("DEBUG", "Frame: ${frameW}x${frameH}, Preview: ${previewW}x${previewH}, Rotation: ${frameRotation}")

        val boxes = detectNV21(nv21, frameW, frameH, frameRotation,this@TakePictureActivity)
        val labels = Array(boxes.size/4) { i -> "face_${i}" }

        runOnUiThread {

            if(!subBinding.ivTakePictureResult.isVisible()){

                when(labels.size){
                    0->{
                        subBinding.tvTopHint.text = "未检测到人脸"
                        isShuttable = false
                    }

                    1->{
                        subBinding.tvTopHint.text = "脸部与摄像头平视，识别中"
                        isShuttable = true
                    }

                    else->{
                        subBinding.tvTopHint.text = "不要多人同时采集"
                        isShuttable = false
                    }
                }
            }

        }


        if (viewW > 0 && viewH > 0) {
            runOnUiThread { subBinding.overlay.setData(transformCoordinates(
                boxes = boxes,
                frameW = frameW,
                frameH = frameH,
                frameRotation = frameRotation,
                viewW = viewW,
                viewH = viewH,
                isFrontCamera = isFrontCamera,
                cachedScaleType = cachedScaleType), labels, getColorExt(R.color.redFF0000Alpha80)) }
        }
    }






    override fun onClick(v: View?) {
        subBinding.run {
            when(v){
                ivStartShutter->{
                    if(isShuttable){
                        showLoadingExt("正在拍摄处理中..")
                        takePhoto()
                    }else{
                        ToastUtils.showShort(subBinding.tvTopHint.text)
                    }

                }

                ivSwitchCamera->{
                    switchCamera()
                }

                llTakePictureAgain->{
                    File(resultImagePath).delete()
                    ivTakePictureResult.gone()
                    llBottomActionHint.gone()
                    llBottomHint.visible()
                }

                llCommint->{
                    // 返回结果给 StudentInfoEditActivity
                    if(!resultImagePath.isNullOrEmpty()){
                        val resultIntent = Intent().apply {
                            putExtra("imagePath", resultImagePath)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()

                    }

                }

                else->{}
            }
        }

    }
    private val outputDirectory: File by lazy {
        // 获取保存目录
        val mediaDir = externalMediaDirs.firstOrNull()?.let {
            File(it, resources.getString(R.string.app_name)).apply { mkdirs() }
        }
        if (mediaDir != null && mediaDir.exists()) mediaDir else filesDir
    }

    private fun takePhoto() {
        // 获取ImageCapture用例
        val imageCapture = imageCapture ?: return

        // 创建带时间戳的输出文件
        val photoFile = File(
            outputDirectory,
            SimpleDateFormat(FILENAME_FORMAT, Locale.CHINA)
                .format(System.currentTimeMillis()) + ".jpg"
        )


        // 创建元数据，设置是否需要水平翻转
        val metadata = ImageCapture.Metadata().apply {
            // 如果是前置摄像头，设置水平翻转
            isReversedHorizontal = (cameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA)
        }

        val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile)
            .setMetadata(metadata) // 添加元数据
            .build()



        // 设置图像捕获监听器
        imageCapture.takePicture(
            outputOptions,
            ContextCompat.getMainExecutor(this),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exc: ImageCaptureException) {
                    Log.e(TAG, "Photo capture failed: ${exc.message}", exc)
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    val savedUri = Uri.fromFile(photoFile)
                    val msg = "Photo capture succeeded: $savedUri"
                    zipImgFile(savedUri)



                    // 这里可以处理保存的照片，例如显示在ImageView中或上传到服务器
                }
            })
    }

    private fun zipImgFile(savedUri:Uri){
        Luban.with(this@TakePictureActivity)
            .load<Uri?>(arrayListOf<Uri>(savedUri))
            .ignoreBy(100)
            .setTargetDir(outputDirectory.path)
            .filter(object : CompressionPredicate {
                public override fun apply(path: String): Boolean {
                    return !(TextUtils.isEmpty(path) || path.lowercase(Locale.getDefault())
                        .endsWith(".gif"))
                }
            })
            .setCompressListener(object : OnCompressListener {
                public override fun onStart() {
                }

                public override fun onSuccess(file: File?) {
                    file?.let {
                        copyExif(savedUri.path ?: "", it.path)
                    }

                    ImageViewUtils.loadImg(file?.path?:"",subBinding.ivTakePictureResult)
                    subBinding.llBottomActionHint.visible()
                    subBinding.llBottomHint.gone()
                    subBinding.ivTakePictureResult.visible()
                    subBinding.tvTopHint.text = "拍摄完成"
                    savedUri.path?.let {savedUriPath->
                        File(savedUriPath).delete()
                    }
                    resultImagePath = file?.path?:""
                    dismissLoadingExt()

                }

                public override fun onError(e: Throwable?) {
                    ToastUtils.showShort("拍摄失败，请重试")
                    dismissLoadingExt()
                }
            }).launch()


    }




    private fun hasBackCamera(): Boolean {
        return cameraProvider?.hasCamera(CameraSelector.DEFAULT_BACK_CAMERA) == true
    }

    private fun hasFrontCamera(): Boolean {
        return cameraProvider?.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA) == true
    }
    private fun switchCamera() {

        if (!hasFrontCamera() || !hasBackCamera()) {
            Toast.makeText(this, "不支持切换摄像头", Toast.LENGTH_SHORT).show()
            return
        }

        cameraSelector = if (cameraSelector == CameraSelector.DEFAULT_BACK_CAMERA) {
            CameraSelector.DEFAULT_FRONT_CAMERA
        } else {
            CameraSelector.DEFAULT_BACK_CAMERA
        }
        bindCamera()
    }

}