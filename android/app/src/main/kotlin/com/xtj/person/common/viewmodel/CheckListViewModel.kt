package com.xtj.person.common.viewmodel

import androidx.lifecycle.MutableLiveData
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.CheckInfoFaceBean
import com.xtj.person.common.bean.CheckListBean
import com.xtj.person.common.ext.rxHttpRequest
import com.xtj.person.common.net.repository.DataRepository

class CheckListViewModel: BaseViewModel() {

    val checkListBeanLiveData = MutableLiveData<CheckListBean>()

    var checkInfoFaceBeanLiveData = MutableLiveData<CheckInfoFaceBean>()

    var checkInfoFaceErrorLiveData = MutableLiveData<Boolean>()

    fun getCheckList(curriculumId:Int,date:String){
        rxHttpRequest {
            onRequest={
                checkListBeanLiveData.value = DataRepository.getCheckList(curriculumId,date).await()
            }
            onError={

            }
        }
    }



    fun getCheckInfoFace(curriculumId:Int){
        rxHttpRequest {
            onRequest={
                checkInfoFaceBeanLiveData.value = DataRepository.getCheckInfoFace(curriculumId).await()
            }

            onError={
                checkInfoFaceErrorLiveData.value = true
            }
        }
    }

}