package com.xtj.person.common.util

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.media.ExifInterface
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import com.xtj.person.common.base.appContext
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

object ImageViewUtils {

    fun isValidContextForGlide(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        if (context is Activity) {
            val activity: Activity = context as Activity
            if (activity.isDestroyed || activity.isFinishing) {
                return false
            }
        }
        return true
    }

    fun loadImg(imagePath: String, errorImageResource:Int,iv: ImageView) {
        Glide.with(iv.context).load(imagePath).error(errorImageResource).into(iv)
    }


    fun loadImg(imagePath: String, iv: ImageView) {
        Glide.with(iv.context).load(imagePath).into(iv)
    }

    fun loadImg(imagePath: String, iv: ImageView, placeHolder: Int) {
        Glide.with(iv.context).load(imagePath).placeholder(placeHolder).into(iv)
    }


    fun loadLocalImg(resourceId: Int, iv: ImageView) {
        Glide.with(iv.context).load(resourceId).into(iv)
    }


    fun createScreenShotTargetDir(): String {
        return appContext.getExternalFilesDir("note/screenShot")?.path ?: ""
    }

    fun deleteScreenShotTargetDirFiles() {
        appContext.getExternalFilesDir("note/screenShot")?.let {
            deleteFileAndFolder(
                it
            )
        }
    }


    /**
     * 删除文件与文件夹
     */
    private fun deleteFileAndFolder(file: File) {
        if (file.isDirectory) {
            file.listFiles()?.let {
                for (i in it.indices) {
                    deleteFileAndFolder(it[i])
                }
            }
            file.delete() // 如要保留文件夹，只删除文件，请注释这行
        } else if (file.exists()) {
            file.delete()
        }
    }


    fun addImageUrlFileToAlbum(imageUrl: String?, context: Context) {
        imageUrl?.let {
            val replaceUrl = imageUrl
//            val replaceUrl = imageUrl.replace(Regex("\\s+"), "")
            Glide.with(context).asBitmap().load(replaceUrl)
                .into(object : CustomTarget<Bitmap?>() {
                    override fun onResourceReady(
                        resource: Bitmap,
                        transition: Transition<in Bitmap?>?,
                    ) {
                        addBitmapToAlbum(resource)
                    }

                    override fun onLoadCleared(placeholder: Drawable?) {
                    }
                })
        }
    }


    fun addBitmapToAlbum(
        bitmap: Bitmap,
        displayName: String = "${System.currentTimeMillis()}.jpg",
        mimeType: String = "image/jpeg",
        compressFormat: Bitmap.CompressFormat = Bitmap.CompressFormat.JPEG
    ) {
        try {


        val values = ContentValues()
        values.put(MediaStore.MediaColumns.DISPLAY_NAME, displayName)
        values.put(MediaStore.MediaColumns.MIME_TYPE, mimeType)
            values.put(MediaStore.MediaColumns.DATE_TAKEN,System.currentTimeMillis())
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            values.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DCIM)
        } else {
            val imageStorageDir = File("${Environment.getExternalStorageDirectory().path}/${Environment.DIRECTORY_DCIM}", "xtjOnline")
            if (!imageStorageDir.exists()) {
                imageStorageDir.mkdirs()
            }
            values.put(
                MediaStore.MediaColumns.DATA,
                "${imageStorageDir}/$displayName"
            )

        }
        val uri =
            appContext.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)
        if (uri != null) {
            val outputStream = appContext.contentResolver.openOutputStream(uri)
            if (outputStream != null) {
                bitmap.compress(compressFormat, 100, outputStream)
                outputStream.close()
            }
        }

        }catch (_:Exception){
        }
    }




    fun saveImageToAppInternalPath(
        bmp: Bitmap,
        externalFilesDirPath: String = createScreenShotTargetDir()
    ): String {
        val file = File("${externalFilesDirPath}/${"${System.currentTimeMillis()}.png"}")
        return try {
            val fos = FileOutputStream(file)
            bmp.compress(Bitmap.CompressFormat.PNG, 100, fos)
            fos.flush()
            fos.close()
            file.absolutePath
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            ""
        } catch (e: IOException) {
            e.printStackTrace()
            ""
        }
    }

    fun bitmapToFile(bitmap: Bitmap, file: File): File {
        var out: FileOutputStream? = null
        try {
            out = FileOutputStream(file)
            // 压缩成 JPEG 格式（100 表示无损压缩，可根据需求改成 PNG/WebP）
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
            out.flush()
        } finally {
            out?.close()
        }




        return file
    }


    /**
     * 复制原图Exif信息到压缩后图片的Exif信息
     * @param sourcePath 原图路径
     * @param targetPath 目标图片路径
     */
     fun copyExif(sourcePath: String, targetPath: String) {
        try {
            val source = ExifInterface(sourcePath)
            val target = ExifInterface(targetPath)
            source.javaClass.declaredFields.forEach { member ->//获取ExifInterface类的属性并遍历
                member.isAccessible = true
                val tag = member.get(source)//获取原图EXIF实例种的成员变量
                if (member.name.startsWith("TAG_") && tag is String) {//判断变量是否以TAG开头，并且是String类型
                    target.setAttribute(tag, source.getAttribute(tag))//设置压缩图的EXIF信息
                    target.saveAttributes()//保存属性更改
                }
            }
        } catch (e: Exception) {
        }
    }

}