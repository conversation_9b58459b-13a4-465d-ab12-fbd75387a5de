package com.xtj.person.common.viewmodel

import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.CheckInfoFaceData
import com.xtj.person.common.bean.FaceEntry


/**
 * 专门发送全局的消息
 */
class EventViewModel : BaseViewModel() {
    companion object {



    }

    val faceEntries = mutableListOf<FaceEntry>()



     var serverFaceDataList:ArrayList<CheckInfoFaceData> = arrayListOf()
}