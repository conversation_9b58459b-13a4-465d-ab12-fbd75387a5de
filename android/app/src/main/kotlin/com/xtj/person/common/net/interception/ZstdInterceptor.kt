package com.xtj.person.common.net.interception


import android.util.Log
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.ResponseBody.Companion.toResponseBody

import okhttp3.Interceptor
import okhttp3.Response
import com.github.luben.zstd.ZstdInputStream
import com.xtj.person.common.ext.toJsonStr
import java.io.ByteArrayInputStream

class ZstdInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)
        val encoding = response.header("Content-Encoding")?.lowercase()
        if (encoding != "zstd" && encoding != "zst") {
            return response
        }

        val bodyBytes = response.body?.bytes() ?: return response
        val decompressed = ZstdInputStream(ByteArrayInputStream(bodyBytes)).use { stream ->
            stream.readBytes()
        }

        val mediaType = response.body?.contentType() ?: "application/json; charset=utf-8".toMediaTypeOrNull()
        val newBody = decompressed.toResponseBody(mediaType)
        return response.newBuilder()
            .removeHeader("Content-Encoding")
            .header("Content-Length", decompressed.size.toString())
            .body(newBody)
            .build()
    }
}