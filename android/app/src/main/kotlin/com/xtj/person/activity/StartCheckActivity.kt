package com.xtj.person.activity

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.util.Rational
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.core.Preview
import androidx.camera.core.UseCaseGroup
import androidx.camera.core.ViewPort
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.CameraActivity
import com.xtj.person.R
import com.xtj.person.activity.TakePictureActivity
import com.xtj.person.adapter.RvNewestCheckInResultAdapter
import com.xtj.person.common.base.eventViewModel
import com.xtj.person.common.bean.CheckInfoFaceData
import com.xtj.person.common.bean.FaceEntry
import com.xtj.person.common.bean.StartCheckInResultBean
import com.xtj.person.common.bean.StartCheckInResultItem
import com.xtj.person.common.bean.WaitUploadingFaceBean
import com.xtj.person.common.ext.dismissLoadingExt
import com.xtj.person.common.ext.getColorExt
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.toJsonStr
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.CameraUtils.chooseAspectRatio
import com.xtj.person.common.util.CameraUtils.cosine
import com.xtj.person.common.util.CameraUtils.cropBitmap
import com.xtj.person.common.util.CameraUtils.expandCropArea
import com.xtj.person.common.util.CameraUtils.imageProxyToBitmap
import com.xtj.person.common.util.CameraUtils.mirrorBitmap
import com.xtj.person.common.util.CameraUtils.rotateBitmap
import com.xtj.person.common.util.CameraUtils.transformCoordinates
import com.xtj.person.common.util.CameraUtils.yuv420888ToNV21
import com.xtj.person.common.util.DateUtils.getSignResultHourMiniute
import com.xtj.person.common.util.ImageViewUtils
import com.xtj.person.common.util.ImageViewUtils.bitmapToFile
import com.xtj.person.common.util.ImageViewUtils.copyExif
import com.xtj.person.common.util.JniUtils.detectNV21
import com.xtj.person.common.util.JniUtils.extractNV21
import com.xtj.person.common.util.JniUtils.seetaSimilarity
import com.xtj.person.common.util.TTSUtil
import com.xtj.person.common.util.TTSUtil.TTSListener
import com.xtj.person.common.viewmodel.StartCheckViewModel
import com.xtj.person.common.widget.GridSpacingItemDecoration
import com.xtj.person.databinding.ActivityStartCheckLayoutBinding
import top.zibin.luban.CompressionPredicate
import top.zibin.luban.Luban
import top.zibin.luban.OnCompressListener
import java.io.File
import java.text.SimpleDateFormat
import java.util.LinkedList
import java.util.Locale
import java.util.concurrent.Executors

class StartCheckActivity: BaseVmActivity<StartCheckViewModel, ActivityStartCheckLayoutBinding>(), View.OnClickListener {




    private var lastNV21: ByteArray? = null
    private var frameW: Int = 0
    private var frameH: Int = 0
    private var frameRotation: Int = 0
    private var ENABLE_RECOG = true // 启用识别功能
    private var isFrontCamera: Boolean = true
    @Volatile private var cachedScaleType: PreviewView.ScaleType = PreviewView.ScaleType.FILL_START
    @Volatile private var viewW: Int = 0
    @Volatile private var viewH: Int = 0
    private var cameraProvider: ProcessCameraProvider? = null
    private var cameraExecutor = Executors.newSingleThreadExecutor()
    private var camera: Camera? = null



    private var newestCheckInResultBeans:ArrayList<StartCheckInResultItem> = arrayListOf()
    private var rvNewestCheckInResultAdapter: RvNewestCheckInResultAdapter?=null

    private var ttsUtil: TTSUtil? = null

    private val outputDirectory: File by lazy {
        // 获取保存目录
        val mediaDir = externalMediaDirs.firstOrNull()?.let {
            File(it, resources.getString(R.string.app_name)).apply { mkdirs() }
        }
        if (mediaDir != null && mediaDir.exists()) mediaDir else filesDir
    }
    private  val FILENAME_FORMAT = "yyyy-MM-dd-HH-mm-ss-SSS"

    private var currentFaceGuid:String = ""

    override fun getViewBinding(inflater: LayoutInflater): ActivityStartCheckLayoutBinding {

        return ActivityStartCheckLayoutBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {

        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        mViewModel.run {
            curriculumName = intent.getStringExtra("curriculumName")?:""
            period = intent.getStringExtra("period")?:""
            curriculumId = intent.getIntExtra("curriculumId",-1)
            timeTitle = intent.getStringExtra("timeTitle")?:""
        }


        subBinding.run {
            include.title.text = mViewModel.curriculumName
            include.backBtn.setOnClickListener { finish() }
            include.rightBtn.text="人脸录入"
            include.rightBtn.setOnClickListener {
               var intent = Intent(this@StartCheckActivity,QueryUserByPhoneActivity::class.java)
                startActivity(intent)
            }
            cachedScaleType = preview.scaleType
            preview.addOnLayoutChangeListener { v, _, _, _, _, _, _, _, _ ->
                viewW = v.width
                viewH = v.height
            }
            tvCheckInRecord.paintFlags = tvCheckInRecord.paintFlags or Paint.UNDERLINE_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG

            tvCheckInRecord.setOnClickListener(this@StartCheckActivity)

            rvNewestResult.layoutManager = GridLayoutManager(this@StartCheckActivity,4)
            rvNewestCheckInResultAdapter = RvNewestCheckInResultAdapter(newestCheckInResultBeans)
            rvNewestResult.adapter = rvNewestCheckInResultAdapter

            // 加间距 (比如 16dp 转 px)
            val spacing = resources.getDimensionPixelSize(R.dimen.dp_16)
            rvNewestResult.addItemDecoration(GridSpacingItemDecoration(4, spacing, true))

        }

        ensurePermissionsAndStart()

//        mViewModel.getNewestCheckInResult(curriculumId,period)



        ttsUtil =  TTSUtil(this@StartCheckActivity,object:TTSListener{
            override fun onInitSuccess() {

//                subBinding.include.title.setOnClickListener {
//                    ttsUtil?.speak("徐鹏请联系班主任",System.currentTimeMillis().toString())
//                }

                Log.d("ttsUtil","tts-onInitSuccess")
            }

            override fun onInitFailure() {
                Log.d("ttsUtil","tts-onInitFailure")
            }

            override fun onSpeechStart() {

                Log.d("ttsUtil","tts-onSpeechStart")
            }

            override fun onSpeechDone() {
                Log.d("ttsUtil","tts-onSpeechDone")
            }

            override fun onSpeechError(errorMessage: String?) {
                ToastUtils.showShort("语音TTS初始化失败")
                Log.d("ttsUtil","tts-onSpeechError")
            }
        })

    }


    private fun ensurePermissionsAndStart() {
        val perms = arrayOf(Manifest.permission.CAMERA)
        val need = perms.any { ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED }
        if (need) ActivityCompat.requestPermissions(this, perms, 100)
        bindCamera()
    }


    override fun onRestart() {
        super.onRestart()
        ensurePermissionsAndStart()
    }


    private fun bindCamera() {
        val providerFuture = ProcessCameraProvider.getInstance(this)
        providerFuture.addListener({
            val provider = providerFuture.get()
            // Choose aspect ratio closest to screen for larger FOV
            val dm = resources.displayMetrics
            val screenW = dm.widthPixels
            val screenH = dm.heightPixels
            val ratio = chooseAspectRatio(screenW, screenH)

            val preview = Preview.Builder()
                .setTargetAspectRatio(ratio)
                .setTargetRotation(subBinding.preview.display.rotation)
                .build()
            preview.setSurfaceProvider(subBinding.preview.surfaceProvider)

            // Fill the view to mimic native camera preview experience
            subBinding.preview.scaleType = PreviewView.ScaleType.FILL_CENTER
            // Update cached scale type after setting
            cachedScaleType = subBinding.preview.scaleType

            val analyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setTargetAspectRatio(ratio)
                .setTargetRotation(subBinding.preview.display.rotation)
                .build()
            cameraExecutor = Executors.newSingleThreadExecutor()
            analyzer.setAnalyzer(cameraExecutor) { image ->
                analyzeFrame(image)
                image.close()
            }

            val cameraSelector = CameraSelector.DEFAULT_FRONT_CAMERA
            isFrontCamera = (cameraSelector == CameraSelector.DEFAULT_FRONT_CAMERA)
            try {
                provider.unbindAll()
                // Use a shared ViewPort so preview and analysis have consistent crop/FOV
                val vpW = if (viewW > 0) viewW else screenW
                val vpH = if (viewH > 0) viewH else screenH
                val rotation = subBinding.preview.display.rotation
                val viewPort = ViewPort.Builder(Rational(vpW, vpH), rotation)
                    .setScaleType(ViewPort.FILL_CENTER)
                    .setLayoutDirection(subBinding.preview.layoutDirection)
                    .build()
                val group = UseCaseGroup.Builder()
                    .addUseCase(preview)
                    .addUseCase(analyzer)
                    .setViewPort(viewPort)
                    .build()
                camera = provider.bindToLifecycle(this as LifecycleOwner, cameraSelector, group)
            } catch (_: Exception) {}
        }, ContextCompat.getMainExecutor(this))
    }


    private fun analyzeFrame(image: ImageProxy) {
        val nv21 = yuv420888ToNV21(image)
        frameW = image.width
        frameH = image.height
        frameRotation = image.imageInfo.rotationDegrees
        lastNV21 = nv21
        val boxes = detectNV21(nv21, frameW, frameH, frameRotation,this@StartCheckActivity)

        ENABLE_RECOG = boxes.isNotEmpty()
        if (ENABLE_RECOG) {
            // recognize best match for each box
            for (i in 0 until (boxes.size/4)) {
                val x=boxes[i*4]; val y=boxes[i*4+1]; val w=boxes[i*4+2]; val h=boxes[i*4+3]
                val feat = extractNV21(nv21, frameW, frameH, x,y,w,h, frameRotation,this@StartCheckActivity)
                //显示人脸图片
                if (feat.isNotEmpty() && eventViewModel.serverFaceDataList.isNotEmpty()) {
                    var bestSeeta = Float.NEGATIVE_INFINITY
                    var bestGuid =""
                    var bestFaceFeature: FloatArray?=null
                    eventViewModel.serverFaceDataList.forEach { serverFaceData->
                        val serverFaceFeatures:ArrayList<FloatArray> = arrayListOf()
                        val originalFeature = serverFaceData.feature.origin
                        if(originalFeature!=null&&originalFeature.isNotEmpty()){
                            serverFaceFeatures.add(originalFeature)
                        }
                        serverFaceData.feature.recent?.forEach { eachFeature->
                            if(eachFeature!=null&&eachFeature.isNotEmpty()){
                                serverFaceFeatures.add(eachFeature)
                            }
                        }
                        serverFaceFeatures.forEach { everyFeature->
                            val s  = try { seetaSimilarity(feat, everyFeature,this@StartCheckActivity) } catch (_: Throwable) { Float.NaN }
                            if (s > bestSeeta) {
                                bestSeeta = s
                                bestFaceFeature = everyFeature
                                bestGuid = serverFaceData.guid
                            }

                        }

                    }

                    if (bestSeeta > 0.7f) {
                        saveOnlyFaceBitmap(image,boxes,i,feat)
                        showFaceBitmap(image,boxes,i,feat,bestGuid)
                    }
                }
            }
        }

        // Transform coordinates from image analysis to preview display
        val transformedBoxes = transformCoordinates(
            boxes = boxes,
            frameW = frameW,
            frameH = frameH,
            frameRotation = frameRotation,
            viewW = viewW,
            viewH = viewH,
            isFrontCamera = isFrontCamera,
            cachedScaleType = cachedScaleType)
        if (viewW > 0 && viewH > 0) {
            runOnUiThread { subBinding.overlay.setData(transformedBoxes, emptyArray<String>(), getColorExt(R.color.redFF0000Alpha80)) }
        }
    }
    var isCheckInIng:Boolean = false

    var onlyFaceBitmap: Bitmap?=null

    fun saveOnlyFaceBitmap(image: ImageProxy,boxes: IntArray,boxesIndex:Int,feat: FloatArray){
        imageProxyToBitmap(image,this@StartCheckActivity)?.let { bitmap ->
            val rotatedBitmap = rotateBitmap(bitmap, image.imageInfo.rotationDegrees)
            val expandedRect = expandCropArea(
                boxes[boxesIndex*4], boxes[boxesIndex*4+1], boxes[boxesIndex*4+2], boxes[boxesIndex*4+3],
                scale = 0.2f, // 扩大20%
                bitmapWidth = rotatedBitmap.width,
                bitmapHeight = rotatedBitmap.height
            )
            cropBitmap(rotatedBitmap,  expandedRect.left,
                expandedRect.top,
                expandedRect.width(),
                expandedRect.height())?.let { cropBitmap ->
                onlyFaceBitmap=  mirrorBitmap(cropBitmap)
            }
            rotatedBitmap.recycle() // 释放原始 Bitmap
        }
    }



    fun showFaceBitmap(image: ImageProxy, boxes: IntArray, boxesIndex: Int, feat: FloatArray,guid:String) {
        imageProxyToBitmap(image, context = this@StartCheckActivity)?.let { bitmap ->
            // 先旋转整张图
            val rotatedBitmap = rotateBitmap(bitmap, image.imageInfo.rotationDegrees)
            val canvas = Canvas(rotatedBitmap)
            val paint = Paint().apply {
                color = Color.RED
                strokeWidth = 6f
                style = Paint.Style.STROKE
                isAntiAlias = true
            }
            val cornerLen = 50 // 直角长度，可调

            // 获取当前人脸框信息
            val x = boxes[boxesIndex * 4]
            val y = boxes[boxesIndex * 4 + 1]
            val w = boxes[boxesIndex * 4 + 2]
            val h = boxes[boxesIndex * 4 + 3]

            val right = x + w
            val bottom = y + h

            // 画四个角
            // 左上角
            canvas.drawLine(x.toFloat(), y.toFloat(), (x + cornerLen).toFloat(), y.toFloat(), paint)
            canvas.drawLine(x.toFloat(), y.toFloat(), x.toFloat(), (y + cornerLen).toFloat(), paint)
            // 右上角
            canvas.drawLine(right.toFloat(), y.toFloat(), (right - cornerLen).toFloat(), y.toFloat(), paint)
            canvas.drawLine(right.toFloat(), y.toFloat(), right.toFloat(), (y + cornerLen).toFloat(), paint)
            // 左下角
            canvas.drawLine(x.toFloat(), bottom.toFloat(), (x + cornerLen).toFloat(), bottom.toFloat(), paint)
            canvas.drawLine(x.toFloat(), bottom.toFloat(), x.toFloat(), (bottom - cornerLen).toFloat(), paint)
            // 右下角
            canvas.drawLine(right.toFloat(), bottom.toFloat(), (right - cornerLen).toFloat(), bottom.toFloat(), paint)
            canvas.drawLine(right.toFloat(), bottom.toFloat(), right.toFloat(), (bottom - cornerLen).toFloat(), paint)

            // 如果需要镜像
            val finalBitmap = mirrorBitmap(rotatedBitmap)

            runOnUiThread {
                if(currentFaceGuid!=guid){
                    // 创建带时间戳的输出文件
                    val photoFile = File(
                        outputDirectory,
                        SimpleDateFormat(FILENAME_FORMAT, Locale.CHINA)
                            .format(System.currentTimeMillis()) + "xxxx.jpg"
                    )

                    bitmapToFile(finalBitmap,photoFile).let {resultFile->

                        Luban.with(this@StartCheckActivity)
                            .load<File?>(arrayListOf<File>(resultFile))
                            .ignoreBy(100)
                            .setTargetDir(outputDirectory.path)
                            .filter(object : CompressionPredicate {
                                public override fun apply(path: String): Boolean {
                                    return !(TextUtils.isEmpty(path) || path.lowercase(Locale.getDefault())
                                        .endsWith(".gif"))
                                }
                            })
                            .setCompressListener(object : OnCompressListener {
                                public override fun onStart() {
                                }

                                public override fun onSuccess(file: File?) {
                                    file?.let {
                                        copyExif(resultFile.path ?: "", it.path)
                                        Log.d("aaaaaa","save:${it.path}")
                                        resultFile.delete()
                                        waitUploadFaceBeans.offer(WaitUploadingFaceBean(guid, it.path, feat))
                                        if(!isCheckInIng){
                                            tryUploadNextFace()
                                        }

                                    }


                                }

                                public override fun onError(e: Throwable?) {


                                }
                            }).launch()




                        }

                    }


            }

            bitmap.recycle()
            rotatedBitmap.recycle()
        }
    }

var waitUploadFaceBeans = LinkedList<WaitUploadingFaceBean>()





    override fun onClick(v: View?) {

        subBinding.run {
            when(v){
                tvCheckInRecord->{
                    mViewModel.run {
                        var intent = Intent(this@StartCheckActivity, CheckInRecordActivity::class.java)
                        intent.putExtra("curriculumName",curriculumName)
                        intent.putExtra("curriculumId",curriculumId)
                        intent.putExtra("period",period)
                        intent.putExtra("timeTitle",timeTitle)
                        startActivity(intent)
                    }

                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        cameraExecutor.shutdown()
        cameraProvider?.unbindAll()
        ttsUtil?.release()
    }

    override fun initObserver() {
        super.initObserver()
        mViewModel.startCheckInErrorLiveData.observe(this){
            currentFaceGuid = ""

            onCheckInFinished()
        }


        mViewModel.startCheckInResultLiveData.observe(this){

            if(it.code==200){
                if(!it.data.list.isNullOrEmpty()){
                    newestCheckInResultBeans.clear()
                    newestCheckInResultBeans.addAll(it.data.list)
                    rvNewestCheckInResultAdapter?.notifyDataSetChanged()
                }

                subBinding.tvTotalCheckNum.text = it.data.total_sign_count.toString()

                subBinding.run {
                    layoutCheckStatusTop.visible()

                    when(it.data.status){
                        "fail"->{
                            //错误
                            ivCheckStatusCenter.setImageResource(R.drawable.icon_check_status_center_fail)
                            layoutCheckStatusTop.setBackgroundResource(R.drawable.drawable_ff3f0f_10)
                            tvTimeCheckStatusCenter.text = it.data.msg
                        }

                        "no_seat"->{
                            //无座
                            ivCheckStatusCenter.setImageResource(R.drawable.icon_check_status_center_fail)
                            layoutCheckStatusTop.setBackgroundResource(R.drawable.drawable_ff7700_10)
                            tvTimeCheckStatusCenter.text = "考勤时间:${getSignResultHourMiniute(it.data.sign_time)}"
                        }

                        "success"->{
                            //有座
                            ivCheckStatusCenter.setImageResource(R.drawable.icon_check_status_center_success)
                            layoutCheckStatusTop.setBackgroundResource(R.drawable.drawable_20a200_10)
                            tvTimeCheckStatusCenter.text = "考勤时间:${getSignResultHourMiniute(it.data.sign_time)}"
                        }
                    }


                    tvCheckInNameTypeTop.text = it.data.student_name+it.data.curriculum_name
                    tvCheckInSeatTop.text = it.data.msg


                   val ttsContent =  it.data.student_name+it.data.msg
                    ttsUtil?.speak(ttsContent,System.currentTimeMillis().toString())

                    tvNameCheckStatusCenter.text = it.data.student_name



                    ivHeadCheckStatusCenter.setImageBitmap(onlyFaceBitmap)
                    layoutCheckStatusCenter.visible()




                    layoutCheckStatusCenter.postDelayed({
                        layoutCheckStatusTop.gone()
                        layoutCheckStatusCenter.gone()
                        currentFaceGuid = ""
                        onCheckInFinished() },2000)
                }
            }

        }



    }


    fun tryUploadNextFace() {
        if (isCheckInIng) return
        val next = waitUploadFaceBeans.poll() ?: return
        isCheckInIng = true
        currentFaceGuid = next.guid
        mViewModel.startCheckIn(next.guid, next.photoFilePath, feature = next.feature)

    }

    fun onCheckInFinished() {
        isCheckInIng = false
        tryUploadNextFace()
    }

}