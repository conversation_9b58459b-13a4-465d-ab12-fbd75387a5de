package com.xtj.person.common.bean




 data class StartCheckInResultBean(
    val code: Int,
    val `data`: StartCheckInResultData,
    val message: String
)

data class StartCheckInResultData(
    val curriculum_name: String,
    val list: List<StartCheckInResultItem>,
    val msg: String,
    val sign_time: String,
    val status: String,
    val student_name: String,
    val total_sign_count: Int
)

data class StartCheckInResultItem(
    val icon: String,
    val sign_time: String,
    val username: String
)