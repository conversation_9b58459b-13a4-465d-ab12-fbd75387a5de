package com.xtj.person.common.util

import android.util.TypedValue
import android.view.Gravity
import com.blankj.utilcode.util.ToastUtils
import com.effective.android.anchors.task.Task
import com.effective.android.anchors.task.TaskCreator
import com.effective.android.anchors.task.project.Project
import com.kingja.loadsir.callback.SuccessCallback
import com.kingja.loadsir.core.LoadSir
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.ClassicsHeader
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.xtj.person.R
import com.xtj.person.common.base.appContext
import com.xtj.person.common.ext.dp
import com.xtj.person.common.ext.getColorExt
import com.xtj.person.common.net.NetHttpClient
import com.xtj.person.common.net.api.NetUrl
import com.xtj.person.common.net.interception.LogInterceptor
import com.xtj.person.common.net.MyHeadInterceptor
import com.xtj.person.common.net.interception.ZstdInterceptor
import com.xtj.person.common.util.state.EmptyCallback
import com.xtj.person.common.util.state.ErrorCallback
import com.xtj.person.common.util.state.LoadingCallback
import rxhttp.RxHttpPlugins


object MyTaskCreator : TaskCreator {

    override fun createTask(taskName: String): Task {
        return when (taskName) {
            InitNetWork.TASK_ID -> InitNetWork()
            InitComm.TASK_ID -> InitComm()
            InitUtils.TASK_ID -> InitUtils()
            InitToast.TASK_ID -> InitToast()
            InitAppLifecycle.TASK_ID -> InitAppLifecycle()
            else -> InitDefault()
        }
    }
}

class InitDefault : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "0"
    }

    override fun run(name: String) {

    }
}

//初始化网络
class InitNetWork : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "1"
    }

    override fun run(name: String) {
        //传入自己的OKHttpClient 并添加了自己的拦截器
//        RxHttp.init(NetHttpClient.getDefaultOkHttpClient().run {
//            addInterceptor(MyHeadInterceptor())//自定义头部拦截器
//            addInterceptor(LogInterceptor())//添加Log拦截器
//        }.build())



        RxHttpPlugins.init(NetHttpClient.getDefaultOkHttpClient().run {
            addInterceptor(MyHeadInterceptor())//自定义头部拦截器
            addInterceptor(LogInterceptor())//添加Log拦截器
            addInterceptor(ZstdInterceptor())
        }.build()).setDebug(!NetUrl.makeApkParams.isOfficial)
    }
}

//初始化常用控件类
class InitComm : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "2"
    }

    override fun run(name: String) {



        SmartRefreshLayout.setDefaultRefreshInitializer { context, layout ->
            //设置 SmartRefreshLayout 通用配置
            layout.setEnableScrollContentWhenLoaded(true)//是否在加载完成时滚动列表显示新的内容
        }
        SmartRefreshLayout.setDefaultRefreshHeaderCreator { context, _ ->
            //设置 Head
            ClassicsHeader(context).apply {
                setAccentColor(getColorExt(R.color.black))
                setTextSizeTitle(TypedValue.COMPLEX_UNIT_DIP, 12f)
                setTextSizeTime(TypedValue.COMPLEX_UNIT_DIP, 12f)
            }
        }
        SmartRefreshLayout.setDefaultRefreshFooterCreator { context, _ ->
            //设置 Footer
            ClassicsFooter(context).apply {
                setAccentColor(getColorExt(R.color.black))
                setTextSizeTitle(TypedValue.COMPLEX_UNIT_DIP, 12f)
            }
        }
        //注册界面状态管理
        LoadSir.beginBuilder()
            .addCallback(ErrorCallback())
            .addCallback(EmptyCallback())
            .addCallback(LoadingCallback())
            .setDefaultCallback(SuccessCallback::class.java)
            .commit()
    }
}

//初始化Utils
class InitUtils : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "3"
    }

    override fun run(name: String) {
        CoilHolder.init(appContext)
    }
}

//初始化Toast
class InitToast : Task(TASK_ID, false) {
    companion object {
        const val TASK_ID = "4"
    }

    override fun run(name: String) {
        //初始化吐司 这个吐司必须要主线程中初始化
        ToastUtils.getDefaultMaker().setGravity(Gravity.CENTER, 0, 60.dp)
    }
}

//初始化AppLifecycle
class InitAppLifecycle : Task(TASK_ID, true) {
    companion object {
        const val TASK_ID = "5"
    }

    override fun run(name: String) {
        //注册全局的Activity生命周期管理
        appContext.registerActivityLifecycleCallbacks(KtxActivityLifecycleCallbacks())
    }
}

class AppTaskFactory : Project.TaskFactory(MyTaskCreator)
