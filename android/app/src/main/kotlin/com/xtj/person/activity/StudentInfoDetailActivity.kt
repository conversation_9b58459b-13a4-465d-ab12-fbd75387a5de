package com.xtj.person.activity

import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.view.LayoutInflater
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.common.ext.getName
import com.xtj.person.common.ext.getSelectSchoolItemBean
import com.xtj.person.common.util.DateUtils.getLastChangeInfoUpdateDate
import com.xtj.person.common.util.ImageViewUtils
import com.xtj.person.common.viewmodel.StudentInfoDetailViewModel
import com.xtj.person.common.widget.WaterMarkBg
import com.xtj.person.databinding.ActivityStudentInfoDetailBinding
import java.text.SimpleDateFormat


class StudentInfoDetailActivity: BaseVmActivity<StudentInfoDetailViewModel, ActivityStudentInfoDetailBinding>() {
    var userName = ""
    var idNumber = ""
    var guid = ""
    var icon = ""
    var phoneNum = ""
    var lastUpdateTime = ""

    override fun getViewBinding(inflater: LayoutInflater): ActivityStudentInfoDetailBinding {

        return ActivityStudentInfoDetailBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {

        userName =  intent.getStringExtra("userName" )?:""
        idNumber =  intent.getStringExtra("idNumber" )?:""
        guid =  intent.getStringExtra("guid" )?:""
        icon =  intent.getStringExtra("icon" )?:""
        phoneNum =  intent.getStringExtra("phoneNum" )?:""
        lastUpdateTime = intent.getStringExtra("lastUpdateTime")?:""
        subBinding.run {
            include.title.text = "录入学员信息"
            include.backBtn.setOnClickListener { finish() }
            include.rightBtn.text = "编辑信息"
            include.rightBtn.setOnClickListener {
                var intent = Intent(this@StudentInfoDetailActivity, StudentInfoEditActivity::class.java)
                intent.putExtra("userName",userName)
                intent.putExtra("idNumber",idNumber)
                intent.putExtra("guid",guid)
                intent.putExtra("icon",icon)
                intent.putExtra("phoneNum",phoneNum)

                startActivity(intent)
                finish()
            }

            tvName.text = userName
            tvIdCard.text = idNumber
            tvId.text = guid
            ImageViewUtils.loadImg(icon,ivUserImg)

            val labels: MutableList<String?> = ArrayList<String?>()
            labels.add(getSelectSchoolItemBean()?.name?:"")
            labels.add(getName())
            viewMask.setBackgroundDrawable(WaterMarkBg(this@StudentInfoDetailActivity, labels, -30, 13))

            tvCopyGuid.paintFlags = tvCopyGuid.paintFlags or Paint.UNDERLINE_TEXT_FLAG
            tvCopyGuid.setOnClickListener {
                ClipboardUtils.copyText(guid)
                ToastUtils.showShort("账号ID已复制到粘贴板")
            }

            tvLastUpdateTime.text = getLastChangeInfoUpdateDate(lastUpdateTime)+"上传"





        }
    }
}