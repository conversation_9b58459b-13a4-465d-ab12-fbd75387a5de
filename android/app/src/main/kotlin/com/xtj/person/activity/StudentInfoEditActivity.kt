package com.xtj.person.activity

import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import com.blankj.utilcode.util.ClipboardUtils
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.activity.StudentInfoDetailActivity
import com.xtj.person.common.ext.dismissLoadingExt
import com.xtj.person.common.ext.getName
import com.xtj.person.common.ext.getSelectSchoolItemBean
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.showLoadingExt
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.ImageViewUtils
import com.xtj.person.common.viewmodel.StudentInfoEditViewModel
import com.xtj.person.common.widget.WaterMarkBg
import com.xtj.person.databinding.ActivityStudentInfoEditBinding
import java.io.File

class StudentInfoEditActivity: BaseVmActivity<StudentInfoEditViewModel, ActivityStudentInfoEditBinding>(),
    View.OnClickListener{
    var userName = ""
    var idNumber = ""
    var guid = ""
    var icon = ""
    var phoneNum = ""

    var localIconFile:String = ""


    private val takePictureLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == RESULT_OK) {
                 localIconFile = result.data?.getStringExtra("imagePath")?:""
                ImageViewUtils.loadImg(localIconFile,subBinding.ivUserImg)
                subBinding.ivUploadImgHint.gone()
                subBinding.tvUploadImgHint.gone()
                subBinding.tvChangeImg.visible()
                Log.d("StudentInfoEdit", "拍照返回路径: $localIconFile")
                // 这里可以把 imagePath 设置到 ImageView 或上传服务器
            }
        }


    override fun getViewBinding(inflater: LayoutInflater): ActivityStudentInfoEditBinding {

        return ActivityStudentInfoEditBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {
        userName =  intent.getStringExtra("userName" )?:""
        idNumber =  intent.getStringExtra("idNumber" )?:""
        guid =  intent.getStringExtra("guid" )?:""
        icon =  intent.getStringExtra("icon" )?:""
        phoneNum =  intent.getStringExtra("phoneNum" )?:""


        if(!icon.isNullOrEmpty()){
            subBinding.ivUploadImgHint.gone()
            subBinding.tvUploadImgHint.gone()
            ImageViewUtils.loadImg(icon,subBinding.ivUserImg)
            subBinding.tvChangeImg.visible()
        }else{
            subBinding.tvChangeImg.gone()
            subBinding.ivUploadImgHint.visible()
            subBinding.tvUploadImgHint.visible()
        }

        subBinding.run {
            include.title.text = "录入学员信息"
            include.backBtn.setOnClickListener { finish() }
            tvChangeImg.setOnClickListener(this@StudentInfoEditActivity)

            tvUploadImgHint.setOnClickListener(this@StudentInfoEditActivity)
            ivUploadImgHint.setOnClickListener(this@StudentInfoEditActivity)

            etName.setText(userName)
            etIdCardNum.setText(idNumber)
            tvId.text = guid
            ImageViewUtils.loadImg(icon,ivUserImg)
            val labels: MutableList<String?> = ArrayList<String?>()
            labels.add(getSelectSchoolItemBean()?.name?:"")
            labels.add(getName())

            tvCommit.setOnClickListener(this@StudentInfoEditActivity)
            tvChangeImg.paintFlags = tvChangeImg.paintFlags or Paint.UNDERLINE_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG

            tvCopyGuid.paintFlags = tvCopyGuid.paintFlags or Paint.UNDERLINE_TEXT_FLAG
            tvCopyGuid.setOnClickListener {
                ClipboardUtils.copyText(guid)
                ToastUtils.showShort("账号ID已复制到粘贴板")
            }

            viewMask.setBackgroundDrawable(WaterMarkBg(this@StudentInfoEditActivity, labels, -30, 13))
        }
    }

    override fun onClick(v: View?) {
        subBinding.run {
            when(v){
                tvUploadImgHint,ivUploadImgHint,tvChangeImg->{
                    var intent = Intent(this@StudentInfoEditActivity, TakePictureActivity::class.java)
                    takePictureLauncher.launch(intent)


                }

                tvCommit->{



                    if(etName.text.isNullOrEmpty()){
                        ToastUtils.showShort("请填写姓名")
                    }else if((etName.text.toString()!=userName)&&(etName.text.toString().contains("*"))){
                        ToastUtils.showShort("姓名已修改，请填写正确姓名（不包含*）")
                    }else if(etIdCardNum.text.isNullOrEmpty()){
                        ToastUtils.showShort("请填写身份证号")
                    }else if((etIdCardNum.text.toString()!=idNumber)&&(etIdCardNum.text.contains("*"))){
                        ToastUtils.showShort("身份证号已修改，请填写正确身份证号（不包含*）")
                    }else if(icon.isNullOrEmpty()&&localIconFile.isNullOrEmpty()){
                        ToastUtils.showShort("请上传照片")
                    }else{
                        showLoadingExt("上传中....")
                        var resultUserName = ""
                        if(etName.text.toString()!=userName){
                            resultUserName = etName.text.toString()
                        }

                        var resultIdCardNum =""
                        if(etIdCardNum.text.toString()!=idNumber){
                            resultIdCardNum = etIdCardNum.text.toString()
                        }


                        mViewModel.changeUserInfo(guid,resultUserName,resultIdCardNum,phoneNum,localIconFile)
                    }

                }
            }
        }

    }

    override fun initObserver() {
        super.initObserver()
        mViewModel.changeUserInfoFailLiveData.observe (this){
            dismissLoadingExt()
            ToastUtils.showShort("上传失败")

        }

        mViewModel.changeUserInfoResultLiveData.observe (this){
            dismissLoadingExt()
            if(it.code==200){
                ToastUtils.showShort("上传成功")
                File(localIconFile).delete()

                finish()
            }else{
                if(it.message.isNullOrEmpty()){
                    ToastUtils.showShort("上传失败")
                }else{
                    ToastUtils.showShort(it.message)
                }
            }
        }
    }

}