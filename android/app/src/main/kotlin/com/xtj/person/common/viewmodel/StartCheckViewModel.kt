package com.xtj.person.common.viewmodel

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.xtj.person.common.base.BaseViewModel
import com.xtj.person.common.bean.CheckInfoFaceBean
import com.xtj.person.common.bean.StartCheckInResultBean
import com.xtj.person.common.ext.rxHttpRequest
import com.xtj.person.common.net.repository.DataRepository
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import kotlin.coroutines.resumeWithException

class StartCheckViewModel: BaseViewModel() {

     var curriculumId:Int = -1
     var curriculumName:String=""
     var period:String = ""
     var timeTitle:String = ""


    var startCheckInResultLiveData = MutableLiveData<StartCheckInResultBean>()

    var startCheckInErrorLiveData = MutableLiveData<Boolean>()





    fun startCheckIn(guid:String,faceFilePath:String,feature: FloatArray){
        rxHttpRequest {
            onRequest={

                startCheckInResultLiveData.value = DataRepository.startCheckIn(guid,curriculumId.toString(),period,faceFilePath,feature).await()
                File(faceFilePath).delete()

            }

            onError={
                startCheckInErrorLiveData.value = false

            }

        }

    }









}