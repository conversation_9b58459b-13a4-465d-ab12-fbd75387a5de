package com.xtj.person.adapter

import android.annotation.SuppressLint
import android.graphics.Paint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.xtj.person.R
import com.xtj.person.common.bean.CheckListItem
import com.xtj.person.common.bean.SchoolListItem
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.OnRvItemClickListener
import com.xtj.person.databinding.LayoutRvCheckListItemBinding
import com.xtj.person.databinding.LayoutRvSelectLocationItemBinding
import com.xtj.person.databinding.LayoutSelectLocationDialogFragmentBinding

class RvCheckListAdapter(
    var beans: ArrayList<CheckListItem>
) :
    RecyclerView.Adapter<RvCheckListAdapter.ViewHolder>() {
         var selectPosition = -1
    var onItemClickListener: OnRvItemClickListener? = null


    class ViewHolder(
        var layoutRvCheckListItemBinding:
        LayoutRvCheckListItemBinding
    ) :
        RecyclerView.ViewHolder(layoutRvCheckListItemBinding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutRvCheckListItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }



    override fun getItemCount(): Int {
        return beans.size

    }


    @SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.layoutRvCheckListItemBinding.run {
            beans[holder.adapterPosition].run {
                tvItemTime.text = title
                tvItemCourseName.text = curriculum_name
                tvItemClassRoom.text = classroom_name
                tvItemCheckInNum.text = sign_in_number.toString()
                tvItemStartCheckIn.paintFlags = tvItemStartCheckIn.paintFlags or Paint.UNDERLINE_TEXT_FLAG
                layoutCheckListItem.setOnClickListener {
                    onItemClickListener?.onClickItem(position)
                }

                when(period){
                    "上午"->{
                        ivTimeBg.setImageResource(R.drawable.bg_check_list_item_morning)
                    }

                    "下午"->{
                        ivTimeBg.setImageResource(R.drawable.bg_check_list_item_noon)
                    }

                    "晚上"->{
                        ivTimeBg.setImageResource(R.drawable.bg_check_list_item_after_noon)
                    }
                    else -> {

                    }
                }


        }
    }

    }


}