package com.xtj.person.adapter

import android.annotation.SuppressLint
import android.graphics.Paint
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.xtj.person.R
import com.xtj.person.common.bean.CheckListItem
import com.xtj.person.common.bean.SchoolListItem
import com.xtj.person.common.bean.StartCheckInResultItem
import com.xtj.person.common.ext.gone
import com.xtj.person.common.ext.visible
import com.xtj.person.common.util.DateUtils.getSignResultHourMiniute
import com.xtj.person.common.util.ImageViewUtils
import com.xtj.person.common.util.OnRvItemClickListener
import com.xtj.person.databinding.LayoutRvCheckListItemBinding
import com.xtj.person.databinding.LayoutRvNewestCheckInResultItemBinding
import com.xtj.person.databinding.LayoutRvSelectLocationItemBinding
import com.xtj.person.databinding.LayoutSelectLocationDialogFragmentBinding

class RvNewestCheckInResultAdapter(
    var beans: ArrayList<StartCheckInResultItem>
) :
    RecyclerView.Adapter<RvNewestCheckInResultAdapter.ViewHolder>() {
         var selectPosition = -1
    var onItemClickListener: OnRvItemClickListener? = null


    class ViewHolder(
        var layoutRvNewestCheckInResultItemBinding:
        LayoutRvNewestCheckInResultItemBinding
    ) :
        RecyclerView.ViewHolder(layoutRvNewestCheckInResultItemBinding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutRvNewestCheckInResultItemBinding.inflate(
                LayoutInflater.from(parent.context),
                parent,
                false
            )
        )
    }



    override fun getItemCount(): Int {
        return beans.size

    }


    @SuppressLint("SuspiciousIndentation")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        holder.layoutRvNewestCheckInResultItemBinding.run {
            beans[holder.adapterPosition].run {
                ImageViewUtils.loadImg(icon,ivItemHeadBottom)
                tvItemName.text = username
                tvItemCheckTime.text = getSignResultHourMiniute(sign_time)

        }
    }

    }


}