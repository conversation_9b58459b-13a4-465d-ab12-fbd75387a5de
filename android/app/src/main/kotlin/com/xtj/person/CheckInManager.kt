package com.xtj.person



import android.content.Context
import android.content.Intent
import android.util.Log
import com.xtj.person.PersonalActivity
import com.xtj.person.activity.FacialCheckInTodayCourseActivity
import com.xtj.person.common.ext.setLoginToken
import com.xtj.person.common.ext.setName
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

class CheckInManager(private val context: Context) : MethodCallHandler {
    companion object {
        private const val CHANNEL_NAME = "check_in_plugin"
    }

    private var methodChannel: MethodChannel? = null

    /**
     * 初始化MethodChannel
     */
    fun initChannel(messenger: io.flutter.plugin.common.BinaryMessenger) {
        methodChannel = MethodChannel(messenger, CHANNEL_NAME)
        methodChannel?.setMethodCallHandler(this)
    }

    /**
     * 处理从Flutter发送过来的方法调用
     */
    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "example" -> {
                val token = call.argument<String>("token")
                val name = call.argument<String>("name")?:""
                val intent = Intent(context, FacialCheckInTodayCourseActivity::class.java)
                context.startActivity(intent)

                setLoginToken(token?:"")
                setName(name)
                Log.e("mmmmmmmmmmmmmmmm", "onMethodCall: 收到flutter端发送的消息--token:${token}--name:${name}", )
                sendMessageToFlutter("exampleCallBack", null)
                Log.e("mmmmmmmmmmmmmmmm", "onMethodCall: 向flutter端发送消息", )
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    /**
     * 主动向Flutter发送消息
     */
    fun sendMessageToFlutter(method: String, arguments: Any?) {
        methodChannel?.invokeMethod(method, arguments)
    }

    /**
     * 清理资源
     */
    fun dispose() {
        methodChannel?.setMethodCallHandler(null)
        methodChannel = null
    }
}