package com.xtj.person.activity

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import com.blankj.utilcode.util.ToastUtils
import com.xtj.person.common.ext.visible
import com.xtj.person.common.viewmodel.QueryUserByPhoneViewModel
import com.xtj.person.databinding.ActivityQueryUserByPhoneBinding

class QueryUserByPhoneActivity: BaseVmActivity<QueryUserByPhoneViewModel, ActivityQueryUserByPhoneBinding>() {
    override fun getViewBinding(inflater: LayoutInflater): ActivityQueryUserByPhoneBinding {

        return ActivityQueryUserByPhoneBinding.inflate(inflater)
    }

    override fun initView(savedInstanceState: Bundle?) {
        subBinding.run {
            include.title.text = "录入学员信息"
            include.backBtn.setOnClickListener { finish() }

            tvSure.setOnClickListener {

                if(etPhone.text.isNullOrEmpty()){
                    ToastUtils.showShort("请输入手机号")
                }else if(etPhone.text.length!=11){
                    ToastUtils.showShort("手机号格式不正确，请检查")
                }else{
                    mViewModel.queryUserInfoByPhone(etPhone.text.toString())
                }
            }
        }
    }

    override fun initObserver() {
        super.initObserver()

        mViewModel.queryUserInfoByPhoneBeanLiveData.observe (this){
            if(it.code==200){
                if(it.data.no_exists){
                    //信息不存在
                 subBinding.tvErrorHint.visible()
                }else{
                    if(it.data.is_import){
                        //需要导入信息
                        var intent = Intent(this@QueryUserByPhoneActivity, StudentInfoEditActivity::class.java)
                        intent.putExtra("userName",it.data.username)
                        intent.putExtra("idNumber",it.data.id_number)
                        intent.putExtra("guid",it.data.guid)
                        intent.putExtra("icon",it.data.icon)
                        intent.putExtra("phoneNum",subBinding.etPhone.text.toString())

                        startActivity(intent)
                        finish()
                    }else{
                        var intent = Intent(this@QueryUserByPhoneActivity,
                            StudentInfoDetailActivity::class.java)
                        intent.putExtra("userName",it.data.username)
                        intent.putExtra("idNumber",it.data.id_number)
                        intent.putExtra("guid",it.data.guid)
                        intent.putExtra("icon",it.data.icon)
                        intent.putExtra("phoneNum",subBinding.etPhone.text.toString())
                        intent.putExtra("lastUpdateTime",it.data.last_update_time)
                        startActivity(intent)
                        finish()
                    }
                }
            }


        }
    }
}