<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F8FC">
    <include
        android:id="@+id/include"
        layout="@layout/layout_common_title" />
    



    <ScrollView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_width="match_parent"
        android:layout_height="0dp">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_student_info_top"
                android:layout_marginTop="16dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                app:layout_constraintTop_toTopOf="parent"
                android:background="@drawable/drawable_white_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="16dp"
                    android:text="学员信息"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <TextView
                    android:id="@+id/tv_name_title"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toTopOf="@id/et_name"
                    app:layout_constraintBottom_toBottomOf="@id/et_name"
                    android:textColor="#8B90A0"
                    android:textSize="14dp"
                    android:text="学员姓名"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <EditText
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:paddingTop="13dp"
                    android:paddingBottom="13dp"
                    android:paddingStart="21dp"
                    android:background="@drawable/drawable_f7f7f7_10"
                    android:layout_marginStart="10dp"
                    android:id="@+id/et_name"
                    app:layout_constraintStart_toEndOf="@id/tv_name_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_title"
                    android:textColor="#222222"
                    android:inputType="text"
                    android:maxLines="1"
                    android:hint="请输入学员真实姓名"
                    android:textSize="14dp"
                    android:textColorHint="#B6B8C1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>



                <TextView
                    android:id="@+id/tv_id_card_num_title"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toTopOf="@id/et_id_card_num"
                    app:layout_constraintBottom_toBottomOf="@id/et_id_card_num"
                    android:textColor="#8B90A0"
                    android:textSize="14dp"
                    android:text="身份证号"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <EditText
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:paddingTop="13dp"
                    android:paddingBottom="13dp"
                    android:paddingStart="21dp"
                    android:background="@drawable/drawable_f7f7f7_10"
                    android:layout_marginStart="10dp"
                    android:id="@+id/et_id_card_num"
                    app:layout_constraintStart_toEndOf="@id/tv_name_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/et_name"
                    android:textColor="#222222"
                    android:inputType="text"
                    android:maxLines="1"
                    android:hint="请输入学员身份证号"
                    android:textSize="14dp"
                    android:textColorHint="#B6B8C1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"/>





                <TextView
                    android:layout_marginTop="16dp"
                    android:id="@+id/tv_id_title"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toBottomOf="@id/et_id_card_num"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginBottom="16dp"
                    android:textColor="#8B90A0"
                    android:textSize="14dp"
                    android:text="账号ID"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <TextView
                    android:layout_marginStart="24dp"
                    android:id="@+id/tv_id"
                    app:layout_constraintStart_toEndOf="@id/tv_id_title"
                    app:layout_constraintTop_toTopOf="@id/tv_id_title"
                    app:layout_constraintBottom_toBottomOf="@id/tv_id_title"
                    android:textColor="#222222"
                    android:textSize="14dp"
                    android:text=""
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/tv_copy_guid"
                    android:layout_marginStart="7dp"
                    app:layout_constraintBottom_toBottomOf="@id/tv_id"
                    app:layout_constraintTop_toTopOf="@id/tv_id"
                    app:layout_constraintStart_toEndOf="@id/tv_id"
                    android:textColor="@color/color_0054FF"
                    android:textSize="14dp"
                    android:text="复制"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>




            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_marginBottom="90dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:id="@+id/layout_student_info_img"
                android:layout_marginTop="16dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                app:layout_constraintTop_toBottomOf="@id/layout_student_info_top"
                android:background="@drawable/drawable_white_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


                <TextView
                    android:id="@+id/tv_img_title"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="16dp"
                    android:text="学员照片"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <androidx.constraintlayout.utils.widget.ImageFilterView
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    app:round="15dp"
                    android:id="@+id/iv_user_img"
                    android:src="@color/color_F5F5F5"
                    app:layout_constraintTop_toBottomOf="@id/tv_img_title"
                    app:layout_constraintDimensionRatio="1:1"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"/>


                <ImageView
                    android:id="@+id/iv_upload_img_hint"
                    app:layout_constraintEnd_toEndOf="@id/iv_user_img"
                    app:layout_constraintStart_toStartOf="@id/iv_user_img"
                    app:layout_constraintVertical_chainStyle="packed"
                    app:layout_constraintTop_toTopOf="@id/iv_user_img"
                    app:layout_constraintBottom_toTopOf="@id/tv_upload_img_hint"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/icon_face_upload_img_camera"
                    />


                <TextView
                    android:id="@+id/tv_upload_img_hint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="点击照片上传"
                    android:textSize="14dp"
                    android:textColor="@color/color_333333"
                    app:layout_constraintTop_toBottomOf="@id/iv_upload_img_hint"
                    app:layout_constraintStart_toStartOf="@id/iv_upload_img_hint"
                    app:layout_constraintBottom_toBottomOf="@id/iv_user_img"
                    app:layout_constraintEnd_toEndOf="@id/iv_upload_img_hint"
                    android:paddingTop="11.5dp"
                    />


                <TextView
                    app:layout_constraintHorizontal_chainStyle="packed"
                    app:layout_constraintEnd_toStartOf="@id/tv_change_img"
                    android:id="@+id/tv_upload_img_hint_info"
                    android:layout_marginTop="15.5dp"
                    android:textColor="#909090"
                    android:textSize="13dp"
                    android:layout_marginBottom="16.5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_user_img"
                    android:text="系统将依据此照片进行人脸打卡识别"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:visibility="gone"
                    android:layout_marginStart="6dp"
                    android:id="@+id/tv_change_img"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/tv_upload_img_hint_info"
                    app:layout_constraintBottom_toBottomOf="@id/tv_upload_img_hint_info"
                    app:layout_constraintTop_toTopOf="@id/tv_upload_img_hint_info"
                    android:textSize="13dp"
                    android:textColor="#0054FF"
                    android:text="更换照片"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_commit"
                android:layout_marginTop="11dp"
                app:layout_constraintTop_toBottomOf="@id/layout_student_info_img"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="15dp"
                android:paddingBottom="15dp"
                android:layout_marginStart="24dp"
                android:layout_marginEnd="24dp"
                android:gravity="center"
                android:text="确定"
                android:textSize="16dp"
                android:textColor="@color/white"
                android:background="@drawable/drawable_0054ff_16"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <View
        android:id="@+id/view_mask"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="0dp"
        android:layout_height="0dp"/>





</androidx.constraintlayout.widget.ConstraintLayout>