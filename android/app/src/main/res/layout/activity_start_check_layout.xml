<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F8FC">
    <include
        android:id="@+id/include"
        layout="@layout/layout_common_title" />



    <androidx.camera.view.PreviewView
        android:id="@+id/preview"
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@id/include"
        app:layout_constraintBottom_toTopOf="@id/view_camera_bottom"
        android:layout_height="0dp"/>

    <com.xtj.person.OverlayView
        android:id="@+id/overlay"
        android:layout_width="match_parent"
        app:layout_constraintBottom_toTopOf="@id/view_camera_bottom"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_height="0dp"
        />


    <LinearLayout
        android:visibility="gone"
        android:id="@+id/layout_check_status_top"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="15dp"
        android:background="@drawable/drawable_20a200_10"
        android:weightSum="2"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_width="match_parent"
        android:layout_height="45dp">

        <LinearLayout
            android:gravity="center"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:orientation="horizontal"
            android:layout_height="match_parent">
            <View
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                />
            <View
                android:layout_marginStart="3dp"
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                />

            <TextView
                android:layout_marginStart="27dp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:textSize="13dp"
                android:id="@+id/tv_check_in_name_type_top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>


        </LinearLayout>
        
        <View
            android:layout_gravity="center"
            android:background="@color/white"
            android:layout_width="1dp"
            android:layout_height="15dp"/>

        <LinearLayout
            android:gravity="center"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:orientation="horizontal"
            android:layout_height="match_parent">
        

            <TextView
                android:layout_marginEnd="27dp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:textSize="13dp"
                android:id="@+id/tv_check_in_seat_top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <View
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                />
            <View
                android:layout_marginStart="3dp"
                android:layout_width="4dp"
                android:layout_height="8dp"
                android:background="@drawable/drawable_white_15"
                android:rotation="30"
                />


        </LinearLayout>

    </LinearLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:visibility="gone"
        android:id="@+id/layout_check_status_center"
        android:background="@drawable/drawable_alpah_black_15"
        app:layout_constraintBottom_toTopOf="@id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_marginBottom="11.5dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_height="74.5dp">
        
        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/iv_head_check_status_center"
            app:round="6dp"
            android:layout_marginStart="20dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="50.5dp"
            android:layout_height="50.5dp"/>

        <TextView
            android:id="@+id/tv_name_check_status_center"
            android:layout_marginStart="13.5dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/tv_time_check_status_center"
            app:layout_constraintStart_toEndOf="@id/iv_head_check_status_center"
            android:text=""
            android:textSize="15dp"
            android:textStyle="bold"
            android:textColor="#FFFFFF"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


        <TextView
            android:id="@+id/tv_time_check_status_center"
            android:layout_marginStart="13.5dp"
            app:layout_constraintTop_toBottomOf="@id/tv_name_check_status_center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_head_check_status_center"
            android:textStyle="bold"
            android:textSize="13dp"
            android:textColor="#FFFFFF"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        
        <ImageView
            android:id="@+id/iv_check_status_center"
            android:layout_marginEnd="27dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="65dp"
            android:layout_height="50dp"
            tools:src="@drawable/icon_check_status_center_success"/>


    </androidx.constraintlayout.widget.ConstraintLayout>


    <View
        android:layout_marginTop="20dp"
        android:id="@+id/view_camera_bottom"
        app:layout_constraintTop_toTopOf="@id/layout_bottom"
        app:layout_constraintBottom_toBottomOf="@id/layout_bottom"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_bottom"
        android:background="@drawable/drawable_white_top_25"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="190dp">

        <TextView
            android:layout_marginTop="16dp"
            android:layout_marginStart="16dp"
            android:id="@+id/tv_total_check_num_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textColor="@color/color_242424"
            android:textSize="13dp"
            android:text="累计签到人数:"
            android:textStyle="bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_total_check_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/color_0054FF"
            android:textSize="13dp"
            app:layout_constraintStart_toEndOf="@id/tv_total_check_num_title"
            app:layout_constraintTop_toTopOf="@id/tv_total_check_num_title"
            app:layout_constraintBottom_toBottomOf="@id/tv_total_check_num_title"

            />


        <TextView
            android:textStyle="bold"
            android:layout_marginEnd="16dp"
            android:text="考勤记录"
            app:layout_constraintBottom_toBottomOf="@id/tv_total_check_num_title"
            app:layout_constraintTop_toTopOf="@id/tv_total_check_num_title"
            app:layout_constraintEnd_toEndOf="parent"
            android:textColor="@color/color_0054FF"
            android:textSize="13dp"
            android:id="@+id/tv_check_in_record"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>



                <androidx.recyclerview.widget.RecyclerView
                    android:layout_marginTop="15dp"
                    android:id="@+id/rv_newest_result"
                    app:layout_constraintTop_toBottomOf="@id/tv_check_in_record"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>





    </androidx.constraintlayout.widget.ConstraintLayout>







</androidx.constraintlayout.widget.ConstraintLayout>