<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.camera.view.PreviewView
        android:id="@+id/preview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        />

    <com.xtj.person.OverlayView
        android:id="@+id/overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <!-- 右侧透明日志面板 -->
    <ScrollView
        android:id="@+id/logScroll"
        android:layout_width="200dp"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:fillViewport="true"
        android:background="#40000000"
        android:padding="8dp">
        <TextView
            android:id="@+id/logView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:lineSpacingExtra="2dp"/>
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp"
        android:background="#66000000"
        android:layout_gravity="bottom">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <EditText
                android:id="@+id/nameEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="姓名(可选)"
                android:textColor="#fff"/>
            <Button
                android:id="@+id/btnRegister"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="注册"/>
            <Button
                android:id="@+id/btnClear"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清空"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="6dp">
            <EditText
                android:id="@+id/schoolEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="学校ID(school_id)"
                android:text="1"
                android:textColor="#fff"/>
            <EditText
                android:id="@+id/guidEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="GUID(可选)"
                android:textColor="#fff"
                android:layout_marginStart="6dp"/>
            <Button
                android:id="@+id/btnFetchBySchool"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="拉取"
                android:layout_marginStart="6dp"/>
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_face"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginEnd="20dp"
        android:layout_marginTop="20dp"
        android:layout_gravity="top|end"
        />

</FrameLayout>
