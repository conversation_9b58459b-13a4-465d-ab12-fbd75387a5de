<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">


    <data>


    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/drawable_white_top_25"
        >

        <ImageView
            android:layout_marginTop="5dp"
            app:layout_constraintEnd_toEndOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="@id/tv_title"
            android:src="@drawable/icon_select_course_title_bg"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            android:layout_width="86.85dp"
            android:layout_height="15.86dp"/>


        <TextView
            android:id="@+id/tv_title"
            android:textStyle="bold"
            android:layout_marginStart="23.5dp"
            android:layout_marginTop="24dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textColor="#000000"
            android:textSize="17dp"
            android:text="请选择课程"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/iv_close"
            android:src="@drawable/icon_select_course_dialog_close"
            android:layout_marginEnd="30.5dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="@id/tv_title"
            android:layout_width="12.5dp"
            android:layout_height="12.5dp"/>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/layout_search"
            android:layout_marginStart="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginTop="19dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            android:background="@drawable/drawable_333333_strike_10"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_search"
                android:layout_marginEnd="22dp"
                android:layout_width="17dp"
                android:layout_height="wrap_content"
                android:adjustViewBounds="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:src="@drawable/icon_search_black"
                />


            <EditText
                android:background="@color/transparent"
                android:paddingBottom="11.5dp"
                android:paddingTop="11.5dp"
                android:layout_marginStart="21dp"
                android:hint="请输入课程名称"
                android:textColorHint="@color/color_CCCCCC"
                android:textSize="15dp"
                android:textColor="@color/black"
                android:inputType="text"
                android:maxLines="1"
                android:id="@+id/et_course_name"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@id/iv_search"
                android:layout_width="0dp"
                android:layout_height="match_parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintHeight_min="400dp"
            app:layout_constraintTop_toBottomOf="@id/layout_search"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">




            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/nested_scroll_view"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">


                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/layout_all"
                            android:layout_marginTop="14dp"
                            android:layout_marginStart="24dp"
                            android:layout_marginEnd="24dp"
                            app:layout_constraintTop_toTopOf="parent"
                            android:layout_width="match_parent"
                            android:layout_height="60dp">

                            <TextView
                                android:layout_marginTop="16dp"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:text="全部课程"
                                android:textSize="15dp"
                                android:textColor="@color/color_333333"
                                app:layout_constraintTop_toTopOf="parent"
                                android:id="@+id/tv_name"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintEnd_toEndOf="parent"
                                />

                            <ImageView
                                android:visibility="gone"
                                android:id="@+id/iv_select_status"
                                android:layout_marginEnd="6dp"
                                app:layout_constraintBottom_toBottomOf="@id/tv_name"
                                app:layout_constraintTop_toTopOf="@id/tv_name"
                                app:layout_constraintEnd_toEndOf="parent"
                                android:src="@drawable/icon_select_course_dialog_check"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"/>

                            <View
                                android:layout_marginTop="15.5dp"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toBottomOf="@id/tv_name"
                                android:layout_width="0dp"
                                android:layout_height="0.5dp"
                                android:background="#979797"
                                />


                        </androidx.constraintlayout.widget.ConstraintLayout>


                        <androidx.recyclerview.widget.RecyclerView
                            app:layout_constraintTop_toBottomOf="@id/layout_all"
                            android:visibility="visible"
                            android:id="@+id/recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            />

                        <TextView
                            android:visibility="gone"
                            android:id="@+id/tv_no_more"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/recyclerView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="没有更多内容了"
                            android:textSize="12dp"
                            android:textColor="@color/gray999"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            />

                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.core.widget.NestedScrollView>


                <androidx.constraintlayout.widget.Group
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/empty_group"
                    android:visibility="gone"
                    app:constraint_referenced_ids="empty_iv,empty_tv"
                    />

                <ImageView
                    android:id="@+id/empty_iv"
                    android:layout_width="116dp"
                    android:adjustViewBounds="true"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_data_empty_placeholder"
                    app:layout_constraintVertical_bias="0.35"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:gravity="center"
                    android:layout_marginTop="18dp"
                    android:id="@+id/empty_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="暂无数据，这里空空如也～"
                    android:textColor="#464B59"
                    android:textSize="13dp"
                    app:layout_constraintTop_toBottomOf="@id/empty_iv"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />


                <androidx.constraintlayout.widget.Group
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/net_error_group"
                    android:visibility="gone"
                    app:constraint_referenced_ids="net_error_iv,net_error_tv,layout_net_error_refresh"
                    />

                <ImageView
                    android:id="@+id/net_error_iv"
                    android:layout_width="116dp"
                    android:adjustViewBounds="true"
                    android:layout_height="wrap_content"
                    android:src="@drawable/icon_data_net_error"
                    app:layout_constraintVertical_bias="0.35"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />



                <TextView
                    android:gravity="center"
                    android:layout_marginTop="18dp"
                    android:id="@+id/net_error_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="网络加载失败\n请再次刷新或检查网络哦~"
                    android:textColor="#464B59"
                    android:textSize="13dp"
                    app:layout_constraintTop_toBottomOf="@id/net_error_iv"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <com.lihang.ShadowLayout
                    android:id="@+id/layout_net_error_refresh"
                    android:layout_marginTop="10dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:hl_layoutBackground="@color/color_0054FF"
                    app:hl_cornerRadius="5dp"
                    app:layout_constraintStart_toStartOf="@id/net_error_tv"
                    app:layout_constraintEnd_toEndOf="@id/net_error_tv"
                    app:layout_constraintTop_toBottomOf="@id/net_error_tv"
                    >
                    <TextView
                        android:text="刷新"
                        android:textSize="14dp"
                        android:textColor="@color/white"
                        android:paddingStart="15dp"
                        android:paddingEnd="15dp"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />


                </com.lihang.ShadowLayout>



                <ProgressBar
                    android:visibility="gone"
                    android:id="@+id/loading_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintVertical_bias="0.35"
                    android:indeterminateTint="#0054ff"
                    android:indeterminateTintMode="src_atop" />





            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>




    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>