<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F8FC">
        <include
            android:id="@+id/include"
            layout="@layout/layout_common_title" />
        
        <ImageView
            android:id="@+id/iv_loc"
            android:layout_marginStart="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/iv_date"
            app:layout_constraintBottom_toBottomOf="@id/iv_date"
            android:src="@drawable/icon_today_course_loc_gray"
            android:layout_width="11dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:paddingStart="7dp"
            android:id="@+id/tv_loc"
            app:layout_constraintStart_toEndOf="@id/iv_loc"
            app:layout_constraintTop_toTopOf="@id/iv_date"
            app:layout_constraintBottom_toBottomOf="@id/iv_date"
            android:textColor="@color/color_242424"
            android:textSize="13dp"
            android:text="哈尔滨新途径"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        
        <ImageView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:src="@drawable/icon_today_course_arrow_down"
            app:layout_constraintStart_toEndOf="@id/tv_loc"
            app:layout_constraintTop_toTopOf="@id/tv_loc"
            app:layout_constraintBottom_toBottomOf="@id/tv_loc"
            android:layout_marginStart="5dp"
            android:id="@+id/iv_loc_arrow"
            />


        <TextView
            android:layout_marginStart="20dp"
            android:id="@+id/tv_select_course"
            app:layout_constraintStart_toEndOf="@id/iv_loc_arrow"
            app:layout_constraintTop_toTopOf="@id/iv_date"
            app:layout_constraintBottom_toBottomOf="@id/iv_date"
            android:textColor="@color/color_242424"
            android:textSize="13dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="全部课程"
            app:layout_constraintWidth_max="52dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <ImageView
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:src="@drawable/icon_today_course_arrow_down"
            app:layout_constraintStart_toEndOf="@id/tv_select_course"
            app:layout_constraintTop_toTopOf="@id/tv_loc"
            app:layout_constraintBottom_toBottomOf="@id/tv_loc"
            android:layout_marginStart="5dp"
            android:id="@+id/iv_course_arrow"
            />


        <TextView
            android:id="@+id/tv_date"
            android:layout_marginEnd="7dp"
            app:layout_constraintBottom_toBottomOf="@id/iv_date"
            app:layout_constraintTop_toTopOf="@id/iv_date"
            app:layout_constraintEnd_toStartOf="@id/iv_date"
            android:textColor="#8286A3"
            android:textSize="10dp"
            tools:text = "9月9日 星期二"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
        
        
        <ImageView
            android:id="@+id/iv_date"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="11.5dp"
            app:layout_constraintTop_toBottomOf="@id/include"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="27dp"
            android:src="@drawable/icon_today_course_date"
            android:layout_height="wrap_content"/>


        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/smartRefreshLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/iv_date"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">




                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                        <androidx.core.widget.NestedScrollView
                            android:id="@+id/nested_scroll_view"
                            app:layout_constraintTop_toTopOf="parent"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">
                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content">




                                        <androidx.recyclerview.widget.RecyclerView
                                            app:layout_constraintTop_toTopOf="parent"
                                            android:visibility="visible"
                                            android:id="@+id/recyclerView"
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            />

                                        <TextView
                                            android:visibility="gone"
                                            android:id="@+id/tv_no_more"
                                            app:layout_constraintStart_toStartOf="parent"
                                            app:layout_constraintEnd_toEndOf="parent"
                                            app:layout_constraintTop_toBottomOf="@id/recyclerView"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="没有更多内容了"
                                            android:textSize="12dp"
                                            android:textColor="@color/gray999"
                                            android:paddingTop="12dp"
                                            android:paddingBottom="12dp"
                                            />

                                </androidx.constraintlayout.widget.ConstraintLayout>


                        </androidx.core.widget.NestedScrollView>


                        <androidx.constraintlayout.widget.Group
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/empty_group"
                            android:visibility="gone"
                            app:constraint_referenced_ids="empty_iv,empty_tv"
                            />

                        <ImageView
                            android:id="@+id/empty_iv"
                            android:layout_width="116dp"
                            android:adjustViewBounds="true"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_data_empty_placeholder"
                            app:layout_constraintVertical_bias="0.35"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />


                        <TextView
                            android:gravity="center"
                            android:layout_marginTop="18dp"
                            android:id="@+id/empty_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="暂无数据，这里空空如也～"
                            android:textColor="#464B59"
                            android:textSize="13dp"
                            app:layout_constraintTop_toBottomOf="@id/empty_iv"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />


                        <androidx.constraintlayout.widget.Group
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/net_error_group"
                            android:visibility="gone"
                            app:constraint_referenced_ids="net_error_iv,net_error_tv,layout_net_error_refresh"
                            />

                        <ImageView
                            android:id="@+id/net_error_iv"
                            android:layout_width="116dp"
                            android:adjustViewBounds="true"
                            android:layout_height="wrap_content"
                            android:src="@drawable/icon_data_net_error"
                            app:layout_constraintVertical_bias="0.35"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />



                        <TextView
                            android:gravity="center"
                            android:layout_marginTop="18dp"
                            android:id="@+id/net_error_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="网络加载失败\n请再次刷新或检查网络哦~"
                            android:textColor="#464B59"
                            android:textSize="13dp"
                            app:layout_constraintTop_toBottomOf="@id/net_error_iv"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent" />

                        <com.lihang.ShadowLayout
                            android:id="@+id/layout_net_error_refresh"
                            android:layout_marginTop="10dp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            app:hl_layoutBackground="@color/color_0054FF"
                            app:hl_cornerRadius="5dp"
                            app:layout_constraintStart_toStartOf="@id/net_error_tv"
                            app:layout_constraintEnd_toEndOf="@id/net_error_tv"
                            app:layout_constraintTop_toBottomOf="@id/net_error_tv"
                            >
                                <TextView
                                    android:text="刷新"
                                    android:textSize="14dp"
                                    android:textColor="@color/white"
                                    android:paddingStart="15dp"
                                    android:paddingEnd="15dp"
                                    android:paddingTop="5dp"
                                    android:paddingBottom="5dp"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content" />


                        </com.lihang.ShadowLayout>



                        <ProgressBar
                            android:visibility="gone"
                            android:id="@+id/loading_progress"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintVertical_bias="0.35"
                            android:indeterminateTint="#0054ff"
                            android:indeterminateTintMode="src_atop" />





                </androidx.constraintlayout.widget.ConstraintLayout>

        </com.scwang.smart.refresh.layout.SmartRefreshLayout>



</androidx.constraintlayout.widget.ConstraintLayout>