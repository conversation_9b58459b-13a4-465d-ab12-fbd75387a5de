<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F8FC">
    <include
        android:id="@+id/include"
        layout="@layout/layout_common_title" />






    <LinearLayout
        android:visibility="gone"
        android:layout_marginTop="20dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:id="@+id/ll_face_progress"
        app:layout_constraintTop_toBottomOf="@id/include"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/face_download_progress"
            android:layout_weight="1"
            app:indicatorColor="@color/color_0054FF"
            android:progress="0"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="10dp"
            android:layout_width="0dp"
            android:layout_height="5dp"/>

        <TextView
            android:layout_marginEnd="16dp"
            android:textSize="12dp"
            android:textColor="@color/color_333333"
            android:text="获取人脸信息中..."
            android:id="@+id/tv_face_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>


    </LinearLayout>


    <TextView

        android:layout_marginStart="16dp"
        android:id="@+id/tv_select_course"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_date"
        app:layout_constraintBottom_toBottomOf="@id/iv_date"
        android:textColor="@color/color_242424"
        android:textSize="13dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:text="全部课程"
        app:layout_constraintWidth_max="52dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <ImageView
        android:layout_width="8dp"
        android:layout_height="8dp"
        android:src="@drawable/icon_today_course_arrow_down"
        app:layout_constraintStart_toEndOf="@id/tv_select_course"
        app:layout_constraintTop_toTopOf="@id/tv_select_course"
        app:layout_constraintBottom_toBottomOf="@id/tv_select_course"
        android:layout_marginStart="5dp"
        android:id="@+id/iv_course_arrow"
        />


    <TextView
        android:id="@+id/tv_date"
        android:layout_marginEnd="7dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_date"
        app:layout_constraintTop_toTopOf="@id/iv_date"
        app:layout_constraintEnd_toStartOf="@id/iv_date"
        android:textColor="#8286A3"
        android:textSize="10dp"
        tools:text = "9月9日 星期二"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>


    <ImageView

        android:id="@+id/iv_date"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="11.5dp"
        app:layout_constraintTop_toBottomOf="@id/ll_face_progress"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_width="27dp"
        android:src="@drawable/icon_today_course_date"
        android:layout_height="wrap_content"/>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_check_list"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_date"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>






</androidx.constraintlayout.widget.ConstraintLayout>