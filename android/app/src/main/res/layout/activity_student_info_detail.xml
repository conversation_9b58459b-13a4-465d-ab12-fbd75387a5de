<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_F7F8FC">
    <include
        android:id="@+id/include"
        layout="@layout/layout_common_title" />
    
    <View
        android:id="@+id/view_top_bg"
        android:layout_width="match_parent"
        android:layout_height="58.5dp"
        android:background="#D9E5FF"
        app:layout_constraintTop_toBottomOf="@id/include"
        />

    <TextView
        android:id="@+id/tv_user_exists"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="此账号系统已有记录！"
        android:textSize="15dp"
        android:textColor="@color/color_0054FF"
        app:layout_constraintTop_toTopOf="@id/view_top_bg"
        app:layout_constraintBottom_toBottomOf="@id/view_top_bg"
        app:layout_constraintStart_toStartOf="parent"
        android:textStyle="bold"
        android:layout_marginStart="27dp"
        />

    <ImageView
        android:id="@+id/iv_user_exists"
        android:layout_marginEnd="17dp"
        app:layout_constraintBottom_toBottomOf="@id/view_top_bg"
        app:layout_constraintTop_toTopOf="@id/view_top_bg"
        app:layout_constraintEnd_toEndOf="@id/view_top_bg"
        android:src="@drawable/icon_user_exists"
        android:layout_width="66dp"
        android:layout_height="50.5dp"/>

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_user_exists"
        android:visibility="visible"
        app:constraint_referenced_ids="iv_user_exists,tv_user_exists,view_top_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <ScrollView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_top_bg"
        android:layout_width="match_parent"
        android:layout_height="0dp">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_student_info_top"
                android:layout_marginTop="16dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                app:layout_constraintTop_toTopOf="parent"
                android:background="@drawable/drawable_white_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="16dp"
                    android:text="学员信息"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <TextView
                    android:layout_marginTop="12dp"
                    android:id="@+id/tv_name_title"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_title"
                    android:textColor="#8B90A0"
                    android:textSize="14dp"
                    android:text="学员姓名:"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <TextView
                    android:layout_marginStart="10dp"
                    android:id="@+id/tv_name"
                    app:layout_constraintStart_toEndOf="@id/tv_name_title"
                    app:layout_constraintTop_toTopOf="@id/tv_name_title"
                    app:layout_constraintBottom_toBottomOf="@id/tv_name_title"
                    android:textColor="#222222"
                    android:textSize="14dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <TextView
                    android:layout_marginTop="16dp"
                    android:id="@+id/tv_id_card_title"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_name_title"
                    android:textColor="#8B90A0"
                    android:textSize="14dp"
                    android:text="身份证号:"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <TextView
                    android:layout_marginStart="10dp"
                    android:id="@+id/tv_id_card"
                    app:layout_constraintStart_toEndOf="@id/tv_id_card_title"
                    app:layout_constraintTop_toTopOf="@id/tv_id_card_title"
                    app:layout_constraintBottom_toBottomOf="@id/tv_id_card_title"
                    android:textColor="#222222"
                    android:textSize="14dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>



                <TextView
                    android:layout_marginTop="16dp"
                    android:id="@+id/tv_id_title"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_id_card_title"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_marginBottom="16dp"
                    android:textColor="#8B90A0"
                    android:textSize="14dp"
                    android:text="账号ID:"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>


                <TextView
                    android:layout_marginStart="10dp"
                    android:id="@+id/tv_id"
                    app:layout_constraintStart_toEndOf="@id/tv_id_title"
                    app:layout_constraintTop_toTopOf="@id/tv_id_title"
                    app:layout_constraintBottom_toBottomOf="@id/tv_id_title"
                    android:textColor="#222222"
                    android:textSize="14dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:id="@+id/tv_copy_guid"
                    android:layout_marginStart="7dp"
                    app:layout_constraintBottom_toBottomOf="@id/tv_id"
                    app:layout_constraintTop_toTopOf="@id/tv_id"
                    app:layout_constraintStart_toEndOf="@id/tv_id"
                    android:textColor="@color/color_0054FF"
                    android:textSize="14dp"
                    android:text="复制"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>




            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_marginBottom="90dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:id="@+id/layout_student_info_img"
                android:layout_marginTop="16dp"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                app:layout_constraintTop_toBottomOf="@id/layout_student_info_top"
                android:background="@drawable/drawable_white_15"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">


                <TextView
                    android:id="@+id/tv_img_title"
                    android:layout_marginTop="16dp"
                    android:layout_marginStart="16dp"
                    android:text="学员照片"
                    android:textSize="16dp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <androidx.constraintlayout.utils.widget.ImageFilterView
                    android:layout_marginTop="15dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginEnd="15dp"
                    app:round="15dp"
                    android:id="@+id/iv_user_img"
                    app:layout_constraintTop_toBottomOf="@id/tv_img_title"
                    app:layout_constraintDimensionRatio="1:1"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"/>



                <TextView
                    android:id="@+id/tv_last_update_time"
                    android:layout_marginTop="15.5dp"
                    android:textColor="#909090"
                    android:textSize="13dp"
                    android:layout_marginBottom="16.5dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_user_img"
                    tools:text="2025年10月5日 10:37上传"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>



<View
    android:id="@+id/view_mask"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    android:layout_width="0dp"
    android:layout_height="0dp"/>



</androidx.constraintlayout.widget.ConstraintLayout>