import 'dart:convert';
import 'dart:io';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 应用缓存管理器
/// 基于flutter_cache_manager实现的多层缓存策略
class AppCacheManager {
  static AppCacheManager? _instance;
  static AppCacheManager get instance => _instance ??= AppCacheManager._();
  
  AppCacheManager._();

  /// 网络数据缓存管理器 - 7天过期
  static final CacheManager _networkCache = CacheManager(
    Config(
      'network_cache',
      stalePeriod: const Duration(days: 7),
      maxNrOfCacheObjects: 1000,
      repo: JsonCacheInfoRepository(databaseName: 'network_cache'),
      fileSystem: IOFileSystem('network_cache'),
      fileService: HttpFileService(),
    ),
  );

  /// 临时数据缓存管理器 - 30天过期
  static final CacheManager _tempCache = CacheManager(
    Config(
      'temp_cache',
      stalePeriod: const Duration(days: 30),
      maxNrOfCacheObjects: 500,
      repo: JsonCacheInfoRepository(databaseName: 'temp_cache'),
      fileSystem: IOFileSystem('temp_cache'),
      fileService: HttpFileService(),
    ),
  );

  /// 图片缓存管理器 - 30天过期
  static final CacheManager _imageCache = CacheManager(
    Config(
      'image_cache',
      stalePeriod: const Duration(days: 30),
      maxNrOfCacheObjects: 2000,
      repo: JsonCacheInfoRepository(databaseName: 'image_cache'),
      fileSystem: IOFileSystem('image_cache'),
      fileService: HttpFileService(),
    ),
  );

  /// 获取网络缓存管理器
  CacheManager get networkCache => _networkCache;

  /// 获取临时缓存管理器
  CacheManager get tempCache => _tempCache;

  /// 获取图片缓存管理器
  CacheManager get imageCache => _imageCache;

  /// 存储JSON数据到网络缓存
  Future<bool> putNetworkJson(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = json.encode(data);
      final bytes = utf8.encode(jsonString);
      await _networkCache.putFile(
        key,
        bytes,
        fileExtension: 'json',
      );
      return true;
    } catch (e) {
      print('存储网络JSON数据失败: $e');
      return false;
    }
  }

  /// 从网络缓存获取JSON数据
  Future<Map<String, dynamic>?> getNetworkJson(String key) async {
    try {
      final file = await _networkCache.getFileFromCache(key);
      if (file != null) {
        final content = await file.file.readAsString();
        return json.decode(content) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      print('获取网络JSON数据失败: $e');
      return null;
    }
  }

  /// 存储临时JSON数据
  Future<bool> putTempJson(String key, Map<String, dynamic> data) async {
    try {
      final jsonString = json.encode(data);
      final bytes = utf8.encode(jsonString);
      await _tempCache.putFile(
        key,
        bytes,
        fileExtension: 'json',
      );
      return true;
    } catch (e) {
      print('存储临时JSON数据失败: $e');
      return false;
    }
  }

  /// 从临时缓存获取JSON数据
  Future<Map<String, dynamic>?> getTempJson(String key) async {
    try {
      final file = await _tempCache.getFileFromCache(key);
      if (file != null) {
        final content = await file.file.readAsString();
        return json.decode(content) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      print('获取临时JSON数据失败: $e');
      return null;
    }
  }

  /// 存储字符串列表（如搜索历史）
  Future<bool> putStringList(String key, List<String> data) async {
    try {
      final jsonString = json.encode(data);
      final bytes = utf8.encode(jsonString);
      await _tempCache.putFile(
        key,
        bytes,
        fileExtension: 'json',
      );
      return true;
    } catch (e) {
      print('存储字符串列表失败: $e');
      return false;
    }
  }

  /// 获取字符串列表
  Future<List<String>> getStringList(String key) async {
    try {
      final file = await _tempCache.getFileFromCache(key);
      if (file != null) {
        final content = await file.file.readAsString();
        final List<dynamic> list = json.decode(content);
        return list.cast<String>();
      }
      return [];
    } catch (e) {
      print('获取字符串列表失败: $e');
      return [];
    }
  }

  /// 删除指定缓存
  Future<void> removeCache(String key, {CacheType type = CacheType.network}) async {
    try {
      switch (type) {
        case CacheType.network:
          await _networkCache.removeFile(key);
          break;
        case CacheType.temp:
          await _tempCache.removeFile(key);
          break;
        case CacheType.image:
          await _imageCache.removeFile(key);
          break;
      }
    } catch (e) {
      print('删除缓存失败: $e');
    }
  }

  /// 清空所有缓存
  Future<void> clearAllCache() async {
    try {
      await Future.wait([
        _networkCache.emptyCache(),
        _tempCache.emptyCache(),
        _imageCache.emptyCache(),
      ]);
    } catch (e) {
      print('清空缓存失败: $e');
    }
  }

  /// 清空指定类型缓存
  Future<void> clearCache(CacheType type) async {
    try {
      switch (type) {
        case CacheType.network:
          await _networkCache.emptyCache();
          break;
        case CacheType.temp:
          await _tempCache.emptyCache();
          break;
        case CacheType.image:
          await _imageCache.emptyCache();
          break;
      }
    } catch (e) {
      print('清空指定缓存失败: $e');
    }
  }

  /// 获取缓存信息
  Future<Map<String, dynamic>> getCacheInfo() async {
    try {
      // 这里可以添加获取缓存大小、文件数量等信息的逻辑
      return {
        'network_cache_objects': 0, // 实际实现中需要获取真实数据
        'temp_cache_objects': 0,
        'image_cache_objects': 0,
      };
    } catch (e) {
      print('获取缓存信息失败: $e');
      return {};
    }
  }
}

/// 缓存类型枚举
enum CacheType {
  network,  // 网络数据缓存
  temp,     // 临时数据缓存
  image,    // 图片缓存
}

/// 缓存键常量
class CacheKeys {
  // 网络数据缓存键
  static const String contactsKey = 'contacts_key';
  static const String xiaoxinSchoolMenu = 'xiaoxin_school_menu';
  static const String functionMenu = 'function_menu';
  static const String circleCategory = 'circle_category';
  
  // 临时数据缓存键
  static const String searchHistory = 'search_history';
  static const String myCourseSearchHistory = 'my_course_search_history';
  static const String videoSearchHistory = 'video_search_history';
}
