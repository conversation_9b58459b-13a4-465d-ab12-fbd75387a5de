import 'package:npemployee/manager/app_cache_manager.dart';

class LocalCacheManager {
  static LocalCacheManager shared = LocalCacheManager.__();
  LocalCacheManager.__();

  bool flag = false;

  Future<String> init() async {
    flag = true;
    try {
      // flutter_cache_manager 不需要显式初始化
      // 返回空字符串表示初始化成功
      return '';
    } catch (e) {
      print('init cache manager error: $e');
      return '';
    }
  }

  bool checkInitialize() {
    if (flag == false) {
      print("Cache Manager Must be inited");
      return true;
    }
    return false;
  }

  bool putMapByKey(String key, Map<String, dynamic> val, {int maxAge = 0}) {
    if (checkInitialize()) return false;

    // 异步操作，这里使用Future.microtask来避免阻塞
    Future.microtask(() async {
      try {
        await AppCacheManager.instance.putNetworkJson(key, val);
      } catch (e) {
        print('putMapByKey error: $e');
      }
    });

    return true;
  }

  Map<String, dynamic>? getMapWithKey(String key,
      {Map<String, dynamic>? nullValue}) {
    if (checkInitialize()) return nullValue;

    // 由于原方法是同步的，这里需要特殊处理
    // 建议调用方使用新的异步方法 getMapWithKeyAsync
    return nullValue;
  }

  /// 新增异步方法来获取缓存数据
  Future<Map<String, dynamic>?> getMapWithKeyAsync(String key,
      {Map<String, dynamic>? nullValue}) async {
    if (checkInitialize()) return nullValue;

    try {
      final result = await AppCacheManager.instance.getNetworkJson(key);
      return result ?? nullValue;
    } catch (e) {
      print('getMapWithKeyAsync error: $e');
      return nullValue;
    }
  }

  void removeValuesForKeys(List<String> keys) {
    if (checkInitialize()) return;

    Future.microtask(() async {
      try {
        for (String key in keys) {
          await AppCacheManager.instance
              .removeCache(key, type: CacheType.network);
        }
      } catch (e) {
        print('removeValuesForKeys error: $e');
      }
    });
  }

  void clearAll() {
    if (checkInitialize()) return;

    Future.microtask(() async {
      try {
        await AppCacheManager.instance.clearAllCache();
      } catch (e) {
        print('clearAll error: $e');
      }
    });
  }

  void clearMemoryCache() {
    if (checkInitialize()) return;

    Future.microtask(() async {
      try {
        await AppCacheManager.instance.clearCache(CacheType.network);
      } catch (e) {
        print('clearMemoryCache error: $e');
      }
    });
  }
}

class LocalCachekeys {
  //通讯录缓存
  static const String contactsKey = 'contacts_key';
}
