import 'dart:developer';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/network/api.dart';
import 'package:npemployee/network/code.dart';
import 'package:npemployee/network/result_data.dart';

extension HttpManagerExtension on HttpManager {
  void requestUrlWithCache(
    url,
    String? cacheId,
    Function(ResultData)? cacheCallBack,
    Function(ResultData)? successCallBack,
    Function(ResultData)? errorCallBack, {
    params,
    String? method,
    Options? options,
    String? baseUrl,
  }) async {
    final List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());

    if (cacheId != null && cacheId.isNotEmpty) {
      try {
        final map = await LocalCacheManager.shared.getMapWithKeyAsync(cacheId);
        if (cacheCallBack != null && map != null) {
          ResultData resultData = ResultData.fromJson(map);
          cacheCallBack(resultData);
        }
      } catch (e) {
        // 缓存读取失败，继续网络请求
        log("Cache read error: $e");
      }
    }
    if (connectivityResult.contains(ConnectivityResult.none)) {
      ToastUtils.show('网络异常');
      return;
    }
    options ??= Options();
    options.method = method ?? HttpMethod.GET;
    options.headers = {"Connection": "close"};
    if (dio.options.baseUrl.isEmpty) {
      return;
    }
    log("[HttpManager fetch with cache] ${dio.options.baseUrl}" +
        url +
        " ${params.toString()}");

    Response<Map>? response;
    try {
      if (options.method == HttpMethod.GET) {
        response = await dio.get<Map>(
          url,
          queryParameters: params,
          options: options,
        );
      } else if (options.method == HttpMethod.POST) {
        response = await dio.post<Map>(
          url,
          data: params,
          options: options,
        );
      }
      final logStr = '''
url: ${dio.options.baseUrl}$url
params: ${params == null ? "null" : params.toString()}
statusCode: ${response?.statusCode.toString() ?? 'null'}
message: ${response?.data?['msg'] ?? 'null'}
connectivity: ${await ValidatorUtils.getNetworkStatusLog()}
''';
      await ValidatorUtils.writeErrorLog(logStr);

      if (response == null || response.statusCode != 200) {
        log("HTTP error: ${response?.statusMessage}");
        if (errorCallBack != null) {
          errorCallBack(ResultData(
              data: null,
              code: response?.statusCode,
              msg: response?.statusMessage ?? ''));
        }
      } else {
        // log(response.toString());
      }

      ResultData resultData =
          ResultData.fromJson(response?.data! as Map<String, dynamic>);
      if (resultData.code == 0) {
        if (cacheId != null && cacheId.isNotEmpty) {
          LocalCacheManager.shared
              .putMapByKey(cacheId, response?.data as Map<String, dynamic>);
        }
        if (successCallBack != null) {
          successCallBack(resultData);
        }
      } else {
        if (errorCallBack != null) {
          errorCallBack(resultData);
        }
      }
    } on DioException catch (e) {
      log("DioError: ${e.message}");
      final logStr = '''
type: ${e.type}
url: ${dio.options.baseUrl}$url
params: ${params == null ? "null" : params.toString()}
statusCode: ${e.response?.statusCode.toString() ?? 'null'}
message: ${e.response?.statusMessage ?? 'null'}
error: ${e.error.toString()}
connectivity: ${await ValidatorUtils.getNetworkStatusLog()}
''';
      await ValidatorUtils.writeErrorLog(logStr);
      if (e.requestOptions.extra['type'] == 'reject') {
        return;
      }
      if (errorCallBack != null) {
        /*  String errmsg =
            e.response?.statusCode == 403 ? '登录过期，请重新登录' : '未知错误，请联系管理员';
        errorCallBack(
            ResultData(data: null, code: e.response?.statusCode, msg: errmsg)); */
        if (e.response?.statusCode == 403) {
          errorCallBack(ResultData(
              data: null, code: Code.TOKEN_EXPIRATION, msg: "登录过期，请重新登录"));
        } else if (e.response?.statusCode == 429) {
          errorCallBack(ResultData(
              data: null, code: Code.TOO_MANY_REQUEST, msg: "请求太过频繁，请稍后重试"));
        } else if (e.response?.statusCode == null) {
          errorCallBack(ResultData(data: null, code: Code.FAILED, msg: ""));
        } else {
          errorCallBack(
              ResultData(data: null, code: Code.FAILED, msg: "未知错误，请联系管理员"));
        }
      }
      // AppInfo.instance?.showToast('${e.message}');
      // return ResultData(data: null, code: Code.FAILED, msg: '');
    } catch (e) {
      log("Error: ${e.toString()}");
      final logStr = '''
url: ${dio.options.baseUrl}$url
params: ${params == null ? "null" : params.toString()}
statusCode: ${response?.statusCode ?? 'null'}
message: ${e.toString()}
connectivity: ${await ValidatorUtils.getNetworkStatusLog()}
''';
      await ValidatorUtils.writeErrorLog(logStr);
      if (errorCallBack != null) {
        errorCallBack(ResultData(
            data: null, code: response?.statusCode, msg: '请检查网络后重试'));
      }
      // AppInfo.instance?.showToast('$e');
      // return ResultData(data: null, code: Code.FAILED, msg: '');
    }
  }
}
