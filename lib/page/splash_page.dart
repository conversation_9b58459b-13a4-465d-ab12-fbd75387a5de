import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/dialog/privacy_policy_dialog.dart';
import 'package:npemployee/common/widget/easy_refresh_config.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/manager/pedometer_manager.dart';
import 'package:npemployee/methodchannel/AndroidProxy.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(const Duration(milliseconds: 2000));

      if (GlobalPreferences().isShouldShowProxy && Platform.isAndroid) {
        await _showPrivacyPolicyDialog();
      }
      _initServices();
    });
  }

  Future<void> _initServices() async {
    _registerProxyPlugin();

    _initUmeng();

    _initPedometer();

    await _initIM();

    await _initCacheManager();

    await _initDownloader();

    EasyRefreshConfig.init();

    _checkIsLogin();
  }

  void _checkIsLogin() {
    if (AppInfo().hasLogined) {
      //根据不同身份跳转不同页面
      if (GlobalPreferences().userInfo != null) {
        // tabs = '/';
        NavigatorUtils.push(context, CommonRouter.tabs, replace: true);
      } else if (GlobalPreferences().oldUserInfo != null) {
        // loginPage = '/'; //老用户完善页面注册可返回登录页面
        NavigatorUtils.push(context, CommonRouter.loginPage, replace: true);
      } else if (GlobalPreferences().userSubInfo != null) {
        // userSubPage = '/';
        NavigatorUtils.push(context, CommonRouter.userSubPage, replace: true);
      } else if (GlobalPreferences().guestInfo != null) {
        // guestPage = '/';
        NavigatorUtils.push(context, CommonRouter.guestPage, replace: true);
      } else {
        // tabs = '/';
        NavigatorUtils.push(context, CommonRouter.tabs, replace: true);
      }
    } else {
      NavigatorUtils.push(context, CommonRouter.loginPage, replace: true);
    }
  }

  Future<bool> _showPrivacyPolicyDialog() async {
    bool isAgree = false;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PrivacyPolicyDialog(
          onAgree: () {
            isAgree = true;
            GlobalPreferences().isShouldShowProxy = false;
            NavigatorUtils.pop(context);
          },
          onDisagree: () {
            isAgree = false;
            NavigatorUtils.pop(context);
            SystemNavigator.pop(); // 使用 SystemNavigator.pop() 来结束应用程序
          },
        );
      },
    );
    return isAgree;
  }

  void _initUmeng() {
    debugPrint('init ---------- 初始化友盟');
    UmengCommonSdk.initCommon(
        AppInfo().umAndroidId, AppInfo().umIosID, AppInfo().umChannel);
  }

//仅Android，首次打开App，同意权限后再加载插件
  void _registerProxyPlugin() {
    if (Platform.isAndroid) {
      debugPrint('init ---------- 初始化Android插件');
      AndroidProxy.registerProxyPlugin();
    }
  }

  ///初始化Android计步器插件
  void _initPedometer() {
    if (Platform.isAndroid) {
      debugPrint('init ---------- 初始化Android计步器');
      PedometerManager();
    }
  }

  Future<bool> _initIM() async {
    debugPrint('init ---------- 初始化腾讯云SDK');
    final CoreServicesImpl coreInstance = TIMUIKitCore.getInstance();
    bool? initState = await coreInstance.init(
        sdkAppID: AppInfo().imId,
        loglevel: LogLevelEnum.V2TIM_LOG_DEBUG,
        listener: V2TimSDKListener(
          onUserSigExpired: () {
            AppInfo().clearLoginStatu();
            Navigator.of(context).pushNamedAndRemoveUntil(
                CommonRouter.loginPage, (route) => false);
          },
        ));
    if (initState == null || !initState) {
      debugPrint('----- 腾讯云SDK初始化失败 -----');
      return false;
    }
    return true;
  }

  Future<void> _initCacheManager() async {
    debugPrint('init ---------- 初始化缓存管理器');
    final rootDir = await LocalCacheManager.shared.init();
    debugPrint("Cache Manager rootDir = $rootDir");
  }

  Future<void> _initDownloader() async {
    debugPrint('init ---------- 开始初始化下载插件');
    await FlutterDownloader.initialize(
        debug: AppInfo().isDebug, ignoreSsl: true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/png/launch_bac.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 上方 Logo 区域
              Expanded(
                flex: 2,
                child: Center(
                  child: Image.asset(
                    'assets/png/launch_logo.png',
                    width: 120,
                    height: 120,
                    fit: BoxFit.contain,
                  ),
                ),
              ),

              // 中间空白区域
              const Spacer(),

              // 下方文字图片区域
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Image.asset(
                      'assets/png/launch_content.png',
                      width: 200,
                      fit: BoxFit.contain,
                    ),
                    const SizedBox(height: 100),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
