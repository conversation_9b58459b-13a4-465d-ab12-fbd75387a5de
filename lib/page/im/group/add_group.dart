import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/model/im/contact_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/im/group/avatar_grid_generator.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:tencent_cloud_chat_uikit/data_services/message/message_services.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

class ImAddGroup extends StatefulWidget {
  final List<V2TimGroupMemberFullInfo?>? members; // 已在群组中存在的用户
  final String? groupId; //null-创建群组 !null-邀请他人加入

  const ImAddGroup({super.key, this.members, this.groupId});

  @override
  State<ImAddGroup> createState() => _ImAddGroupState();
}

class _ImAddGroupState extends State<ImAddGroup> {
  final List _contacts = [];
  final List<ContactUser> selectedUsers = []; // 选中的人员
  final List<ContactUser> _users = [];
  final List<ContactUser> _allUsers = []; // 存储所有用户
  List<V2TimGroupMemberFullInfo?> _members = [];

  // 分页相关变量
  final int _pageSize = 20;
  int _currentPage = 1;
  bool _hasMore = true;

  // 检查用户是否已在群组中
  bool isUserInGroup(ContactUser user) {
    if (_members.isEmpty) {
      return false;
    }
    return _members.any((member) => member?.userID == user.id.toString());
  }

  late EasyRefreshController _refreshController;

  String searchQuery = '';
  FocusNode searchFocusNode = FocusNode();

  final V2TIMManager _sdkInstance = TIMUIKitCore.getSDKInstance();
  final MessageService _messageService = serviceLocator<MessageService>();
  final CoreServicesImpl _coreInstance = TIMUIKitCore.getInstance();

  List<ContactUser> get filteredItems {
    if (searchQuery.isEmpty) return [];

    return _contacts.expand((contact) {
      List<ContactUser> matchingUsers = [];
      matchingUsers.addAll(_searchContant(contact, searchQuery) ?? []);
      return matchingUsers;
    }).toList();
  }

  List<TextSpan> _highlightOccurrences(String text, String query) {
    if (query.isEmpty) return [TextSpan(text: text)];

    List<TextSpan> spans = [];
    int start = 0;
    int indexOfHighlight;
    while ((indexOfHighlight =
            text.toLowerCase().indexOf(query.toLowerCase(), start)) !=
        -1) {
      if (indexOfHighlight > start) {
        spans.add(TextSpan(text: text.substring(start, indexOfHighlight)));
      }
      spans.add(TextSpan(
        text: text.substring(indexOfHighlight, indexOfHighlight + query.length),
        style: const TextStyle(color: Colors.blue),
      ));
      start = indexOfHighlight + query.length;
    }
    if (start < text.length) {
      spans.add(TextSpan(text: text.substring(start)));
    }
    return spans;
  }

  List<ContactUser>? _searchContant(ContactModel contact, String query) {
    var filteredItems = [];
    _searchRecursively(contact, query, filteredItems);
    return filteredItems.cast();
  }

  ContactModel? findContactByDepartmentName(
      List contacts, String departmentName) {
    for (var contact in contacts) {
      if (contact.department.name == departmentName) {
        return contact;
      }
      if (contact.children != null && contact.children!.isNotEmpty) {
        var result =
            findContactByDepartmentName(contact.children!, departmentName);
        if (result != null) {
          return result;
        }
      }
    }
    return null;
  }

  void _searchRecursively(
      ContactModel contact, String query, List filteredItems) {
    // Check if department name matches
    if (contact.department.name.contains(query)) {
      // Add all users from this department
      if (contact.userList.isNotEmpty) {
        filteredItems.addAll(contact.userList);
      } else {
        for (var child in contact.children ?? []) {
          if (child.userList.isNotEmpty) {
            filteredItems.addAll(child.userList);
          }
        }
      }
    } else {
      // If department doesn't match, check individual users
      for (var user in contact.userList) {
        if (user.name.contains(query)) {
          filteredItems.add(user);
        }
      }
    }
    // Recursively search through children
    for (var child in contact.children ?? []) {
      _searchRecursively(child, query, filteredItems);
    }
  }

  void _getContacts() async {
    try {
      Map<String, dynamic>? res = await LocalCacheManager.shared
          .getMapWithKeyAsync(LocalCachekeys.contactsKey);
      if (res != null) {
        ResultData? data = ResultData.fromJson(res);
        _formatContactsData(data, isCache: false);
      } else {
        EasyLoading.show();
        UserServiceProvider().getContacts(cacheCallBack: (data) {
          _formatContactsData(data, isCache: true);
        }, successCallBack: (data) {
          EasyLoading.dismiss();
          _formatContactsData(data, isCache: false);
        }, errorCallBack: (err) {
          EasyLoading.dismiss();
        });
      }
    } catch (e) {
      // 缓存读取失败，直接请求网络数据
      EasyLoading.show();
      UserServiceProvider().getContacts(cacheCallBack: (data) {
        _formatContactsData(data, isCache: true);
      }, successCallBack: (data) {
        EasyLoading.dismiss();
        _formatContactsData(data, isCache: false);
      }, errorCallBack: (err) {
        EasyLoading.dismiss();
      });
    }
  }

  void _formatContactsData(ResultData? data, {bool isCache = false}) {
    _contacts.clear();
    for (var element in data?.data ?? []) {
      if (element['user_count'] != 0) {
        _contacts.add(ContactModel.fromJson(element));
      }
    }
    if (!isCache) {
      if (mounted) {
        _extractUsersFromContacts(isRefresh: true);
        setState(() {});
      }
    }
  }

  void _extractUsersFromContacts({bool isRefresh = false}) {
    if (isRefresh) {
      _currentPage = 1;
      _users.clear();
      _allUsers.clear();
    }

    void recursiveExtract(List<ContactModel> contacts) {
      for (var contact in contacts) {
        _allUsers.addAll(contact.userList);

        if (contact.children != null && contact.children!.isNotEmpty) {
          recursiveExtract(contact.children!);
        }
      }
    }

    if (_allUsers.isEmpty) {
      recursiveExtract(_contacts.cast<ContactModel>());
    }

    _loadPagedData();
  }

  // 加载分页数据
  void _loadPagedData() {
    int startIndex = (_currentPage - 1) * _pageSize;
    int endIndex = startIndex + _pageSize;

    if (startIndex >= _allUsers.length) {
      _hasMore = false;
      _refreshController.finishLoad(IndicatorResult.noMore);
      return;
    }

    if (endIndex > _allUsers.length) {
      endIndex = _allUsers.length;
      _hasMore = false;
    } else {
      _hasMore = true;
    }

    // 如果是刷新，_users 已经被清空，直接添加数据
    // 如果是加载更多，只添加新的数据
    List<ContactUser> newUsers = _allUsers.sublist(startIndex, endIndex);

    // 过滤掉已经存在的用户，避免重复添加
    List<ContactUser> uniqueNewUsers = newUsers.where((newUser) {
      return !_users.any((existingUser) => existingUser.id == newUser.id);
    }).toList();

    _users.addAll(uniqueNewUsers);
  }

  @override
  void initState() {
    super.initState();
    _members = widget.members ?? [];
    if (_members.isEmpty) {
      V2TimGroupMemberFullInfo me = V2TimGroupMemberFullInfo(
          userID: '${GlobalPreferences().userInfo?.user.id}');
      _members.add(me);
    }
    _refreshController = EasyRefreshController(
        controlFinishRefresh: true, controlFinishLoad: true);
    _getContacts();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (searchFocusNode.hasFocus) {
          searchFocusNode.unfocus();
        }
      },
      child: Scaffold(
        appBar: const CommonNav(title: '添加群成员'),
        body: Column(
          children: [
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: _searchView()),
            SizedBox(height: 27.h),
            Expanded(child: _tabContent()),
            if (selectedUsers.isNotEmpty) _selectedUsersPanel(),
          ],
        ),
      ),
    );
  }

  Widget _searchView() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.5.r),
          color: const Color(0xFFF7F8FD)),
      child: TextField(
        onChanged: (value) {
          setState(() {
            searchQuery = value;
          });
        },
        focusNode: searchFocusNode,
        decoration: InputDecoration(
          hintText: '请输入搜索内容',
          hintStyle: TextStyle(
            color: const Color(0xFFBABABA),
            fontSize: 15.sp,
          ).pfRegular,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 0,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 0,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: Colors.transparent,
              width: 0,
            ),
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: Color(0xFFBABABA),
          ),
        ),
        style: const TextStyle(fontSize: 15),
      ),
    );
  }

  Widget _tabContent() {
    return //如果搜索内容为空 展示正常的通讯录层级
        //如果含有搜索内容 展示搜索结果 -> 有结果就展示结果 无结果展示无数据页面
        (searchQuery.isNotEmpty
            ? (filteredItems.isEmpty
                ? const NoDataPage()
                : ListView.builder(
                    // 这个是搜索后的结果
                    itemCount: filteredItems.length,
                    itemBuilder: _filteredItemsDepartmentItem))
            : EasyRefresh.builder(
                controller: _refreshController,
                onRefresh: () async {
                  // 下拉刷新
                  _extractUsersFromContacts(isRefresh: true);
                  setState(() {});
                  _refreshController.finishRefresh();
                  _refreshController.resetFooter();
                },
                onLoad: _hasMore
                    ? () async {
                        // 上拉加载更多
                        _currentPage++;
                        _extractUsersFromContacts(isRefresh: false);
                        setState(() {});
                        if (_hasMore) {
                          _refreshController.finishLoad();
                        } else {
                          _refreshController.finishLoad(IndicatorResult.noMore);
                        }
                      }
                    : null,
                childBuilder: (context, physics) {
                  return _users.isEmpty
                      ? const NoDataPage()
                      : ListView.builder(
                          physics: physics,
                          itemCount: _users.length,
                          itemBuilder: _userItem);
                }));
  }

  Widget _filteredItemsDepartmentItem(BuildContext _, int index) {
    ContactUser model = filteredItems[index];
    bool isSelected = selectedUsers.any((user) => user.id == model.id);
    bool isInGroup = isUserInGroup(model);

    return GestureDetector(
      onTap: () {
        // 如果用户已在群组中，不允许选择
        if (!isInGroup) {
          setState(() {
            if (isSelected) {
              selectedUsers.removeWhere((user) => user.id == model.id);
            } else {
              selectedUsers.add(model);
            }
          });
        }
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
          margin: EdgeInsets.only(bottom: 25.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 18,
                height: 18,
                child: isInGroup
                    ? Image.asset('assets/png/im/group_add_no_selected.png')
                    : isSelected
                        ? Image.asset('assets/png/im/group_add_selected.png')
                        : Image.asset('assets/png/im/group_add_unselected.png'),
              ),
              SizedBox(width: 19.w),
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: CachedNetworkImage(
                        imageUrl: model.avatar,
                        width: 40,
                        height: 40,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            RichText(
                              text: TextSpan(
                                children: _highlightOccurrences(
                                    model.name, searchQuery),
                                style: TextStyle(
                                  color: const Color(0xFF181818),
                                  fontSize: 16.sp,
                                ).pfRegular,
                              ),
                            ),
                            Text(
                                model.department_roles?.first['roles']
                                        .first['role']['name'] ??
                                    '',
                                style: TextStyle(
                                        color: const Color(0xFF999999),
                                        fontSize: 11.sp)
                                    .pfRegular),
                            RichText(
                              text: TextSpan(
                                children: _highlightOccurrences(
                                    "部门：${model.department_roles?.first['department']['name'] ?? ''}",
                                    searchQuery),
                                style: TextStyle(
                                  color: const Color(0xFF999999),
                                  fontSize: 11.sp,
                                ).pfRegular,
                              ),
                            ),
                          ]),
                    ),
                    SizedBox(width: 16.w),
                  ],
                ),
              ),
            ],
          )),
    );
  }

  Widget _userItem(BuildContext _, int index) {
    ContactUser user = _users[index];
    String avatarStr =
        user.avatar.isEmpty ? ValidatorUtils.testImageUrl : user.avatar;
    bool isSelected =
        selectedUsers.any((selectedUser) => selectedUser.id == user.id);
    bool isInGroup = isUserInGroup(user);

    return GestureDetector(
      onTap: () {
        // 如果用户已在群组中，不允许选择
        if (!isInGroup) {
          setState(() {
            if (isSelected) {
              selectedUsers
                  .removeWhere((selectedUser) => selectedUser.id == user.id);
            } else {
              selectedUsers.add(user);
            }
          });
        }
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
          margin: EdgeInsets.only(bottom: 25.h),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 18,
                height: 18,
                child: isInGroup
                    ? Image.asset('assets/png/im/group_add_no_selected.png')
                    : isSelected
                        ? Image.asset('assets/png/im/group_add_selected.png')
                        : Image.asset('assets/png/im/group_add_unselected.png'),
              ),
              SizedBox(width: 19.w),
              Expanded(
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8.r),
                      child: SizedBox(
                        width: 37,
                        height: 37,
                        child: CachedNetworkImage(imageUrl: avatarStr),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Text(user.name,
                        style: TextStyle(
                                color: const Color(0xFF181818), fontSize: 16.sp)
                            .pfRegular),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Wrap(
                        spacing: 5.w,
                        runSpacing: 5.h,
                        children: [
                          ...user.roles.map(
                            (e) => Container(
                              decoration: BoxDecoration(
                                  color: const Color((0xFF3377FF)),
                                  borderRadius: BorderRadius.circular(3.r)),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 4.w, vertical: 1.h),
                              child: Text(e,
                                  style: TextStyle(
                                          color: Colors.white, fontSize: 10.sp)
                                      .pfRegular),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ],
          )),
    );
  }

  // 底部已选择人员面板
  Widget _selectedUsersPanel() {
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 12.h),
      decoration: BoxDecoration(
        color: 'F2F3F5'.toColor(),
      ),
      child: Row(
        children: [
          Expanded(
              child: SizedBox(
            height: 70.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: selectedUsers.length,
              itemBuilder: (context, index) {
                ContactUser user = selectedUsers[index];
                String avatarStr = user.avatar.isEmpty
                    ? ValidatorUtils.testImageUrl
                    : user.avatar;

                return Container(
                  alignment: Alignment.centerLeft,
                  width: 50.w,
                  child: Column(
                    children: [
                      Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: CachedNetworkImage(
                              imageUrl: avatarStr,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Positioned(
                            right: -5,
                            top: -5,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  selectedUsers.removeAt(index);
                                });
                              },
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.cancel,
                                  color: Color(0xFF3377FF),
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 5.h),
                      Text(
                        user.name,
                        style: TextStyle(
                          color: const Color(0xFF181818),
                          fontSize: 12.sp,
                        ).pfRegular,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              },
            ),
          )),
          SizedBox(width: 10.w),
          GestureDetector(
            onTap: () => _createGroup(),
            child: Container(
              padding: EdgeInsetsDirectional.symmetric(
                  horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                  color: AppTheme.colorBlue,
                  borderRadius: BorderRadius.circular(6.r)),
              child: Text(
                '确定(${selectedUsers.length})',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13.sp,
                ).pfRegular,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _createGroup() async {
    if (selectedUsers.length == 1 && widget.groupId == null) {
      ContactUser u = selectedUsers.first;
      String departName = u.department_roles == null
          ? ''
          : u.department_roles!
              .map((e) => e['department']['name'])
              .toList()
              .join(',');
      String roles = u.roles.join(',');
      NavigatorUtils.push(context, MessageRouter.messageDetailPage, arguments: {
        'con': V2TimConversation(
            conversationID: 'c2c_${u.id}',
            userID: u.id.toString(),
            showName: u.name,
            contactUserInfo: {
              "guid": u.guid,
              "mobile": u.mobile,
              "list": [
                {"department": departName, "roles": roles}
              ],
            },
            type: 1)
      });
    } else {
      String groupType = GroupType.Work;
      String groupName = _getGroupName();
      final groupMember = selectedUsers.map((e) {
        return V2TimGroupMember(
            userID: e.id.toString(),
            role: GroupMemberRoleTypeEnum.V2TIM_GROUP_MEMBER_ROLE_MEMBER);
      }).toList();
      if (widget.groupId == null) {
        final res = await _sdkInstance.getGroupManager().createGroup(
              groupType: groupType,
              groupName: groupName,
              memberList: groupMember,
            );
        if (res.code == 0) {
          final groupID = res.data;
          final conversationID = "group_$groupID";
          final convRes = await _sdkInstance
              .getConversationManager()
              .getConversation(conversationID: conversationID);
          if (convRes.code == 0) {
            await _sendMessageToNewlyCreatedGroup(groupType, groupID!);
            final conversation = convRes.data ??
                V2TimConversation(
                    conversationID: conversationID,
                    type: 2,
                    showName: groupName,
                    groupType: groupType,
                    groupID: groupID);

            // 添加 mounted 检查，避免在异步间隙使用 BuildContext
            if (mounted) {
              NavigatorUtils.push(context, MessageRouter.messageDetailPage,
                  arguments: {'con': conversation});
            }
          }
        } else {
          String errorDes = res.desc;
          if (errorDes ==
              'member list count must be less than or equal to 20') {
            errorDes = '最大邀请人数不能超过20人';
          }
          ToastUtils.show(errorDes);
        }
      } else {
        final userIDs = selectedUsers.map((e) => e.id.toString()).toList();
        final inviteRes = await _sdkInstance
            .getGroupManager()
            .inviteUserToGroup(groupID: widget.groupId!, userList: userIDs);
        if (inviteRes.code == 0) {
          if (mounted) {
            Navigator.of(context)
              ..pop()
              ..pop();
          }
        } else {
          EasyLoading.show(status: inviteRes.desc);
        }
      }
    }
  }

  String _getGroupName() {
    String groupName = selectedUsers.map((e) => e.name).join('、');
    if (groupName.length > 15) {
      groupName = '${groupName.substring(0, 15)}...';
    }
    return groupName;
  }

  _sendMessageToNewlyCreatedGroup(String groupType, String groupID) async {
    final loginUserInfo = _coreInstance.loginUserInfo;
    V2TimMsgCreateInfoResult? res = await _messageService.createCustomMessage(
        data: json.encode({
      "businessID": "group_create",
      "version": 4,
      "opUser": loginUserInfo?.nickName ?? loginUserInfo!.userID,
      "content": groupType == GroupType.Community ? "创建社群" : "创建群组",
      "cmd": groupType == GroupType.Community ? 1 : 0
    }));
    if (res != null) {
      await _messageService.sendMessage(
          id: res.id!, groupID: groupID, receiver: '');
    }
  }
}
