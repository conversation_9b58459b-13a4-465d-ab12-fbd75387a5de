import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/device_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/user_info_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/methodchannel/check_in_channel.dart';

import '../../constants/GlobalPreferences.dart';

class FunctionItem extends StatelessWidget {
  final String title;
  final String image;
  final bool? isDev;
  final bool? isOpen;
  final VoidCallback? onTap;
  const FunctionItem(
      {super.key,
      required this.title,
      required this.image,
      this.isDev,
      this.isOpen,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    int crossAxisCount = DeviceUtils.isTablet(context) ? 6 : 4;

    int itemSpacing = DeviceUtils.isTablet(context) ? 5 : 3;

    double itemWidth = (1.sw - 32.w - (14.w * itemSpacing)) / crossAxisCount;

    return GestureDetector(
      onTap: () {
        if (title == "草原音乐节") {
          CheckInChannel().addListen(exampleCallback: () {
            debugPrint("mmmmmmmmmmmmmmmm, 收到native端发送的消息2");
          });
          Map argument = UserInfoUtils.getValuesMapByKeys("token&school&name");
          CheckInChannel().sentToNative("example", argument);
          return;
        }
        if (isDev ?? false) {
          EasyLoading.showInfo('功能开发中，敬请期待');
        } else if (!isOpen!) {
          EasyLoading.showInfo('活动暂未开启');
        } else {
          if (onTap != null) {
            onTap!();
          }
        }
      }, // Add the callback for the tap event
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF8F8F8),
              borderRadius: BorderRadius.circular(8.r),
            ),

            width: itemWidth,
            // height: 70,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 16),
                CachedNetworkImage(
                  imageUrl: image,
                  width: 25,
                  height: 25,
                  placeholder: (context, url) {
                    return Container(
                      decoration: BoxDecoration(
                          color: const Color(0xFFEEEEEE),
                          borderRadius: BorderRadius.circular(10.r)),
                      width: 25,
                      height: 25,
                    );
                  },
                ),
                /* Image.asset(
                  image,
                  height: 25,
                  width: 25,
                ), */
                const SizedBox(height: 10),
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                          fontSize: 11.sp, color: AppTheme.colorBlackTitle)
                      .pfRegular,
                ),
                SizedBox(height: 11.h),
              ],
            ),
          ),
          if (isDev ?? false)
            Positioned(
              right: 0,
              child: SvgPicture.asset(
                'assets/svg/study/developing.svg',
                height: 15,
              ),
            )
          else if (!isOpen!)
            Positioned(
              right: 0,
              child: SvgPicture.asset(
                'assets/png/function/ic_noopen.svg', // Assuming you have a different asset for open state
                height: 15,
              ),
            ),
        ],
      ),
    );
  }
}
