import 'package:npemployee/constants/GlobalPreferences.dart';

class UserInfoUtils {
  static String get name => GlobalPreferences().userInfo?.user.name ?? '';
  static String get mobile => GlobalPreferences().userInfo?.user.mobile ?? '';
  static String get id_no => GlobalPreferences().userInfo?.user.id_no ?? '';
  static String get departmentType =>
      GlobalPreferences().userInfo?.departmentTypeMin ?? '';
  static String get departmentName =>
      GlobalPreferences().userInfo?.departmentNameMin ?? '';
  static String get avatar => GlobalPreferences().userInfo?.user.avatar ?? '';
  static String get token => GlobalPreferences().tokenValue ?? '';
  static String get guid => GlobalPreferences().userInfo?.user.guid ?? '';
  static int? get departmentId => GlobalPreferences().userInfo?.departmentIdMin;
  static int? get userId => GlobalPreferences().userInfo?.user.id;
  static Map? get userRoleNames => GlobalPreferences().userInfo?.roleNames;
  static Map? get userRoleIds => GlobalPreferences().userInfo?.roleIds;
  static Map get schoolInfo => GlobalPreferences().userInfo?.schoolInfo ?? {};
  static int get timeStamp => DateTime.now().millisecondsSinceEpoch;

  static Map<String, dynamic> getValuesMapByKeys(String keys) {
    final keyList = keys.split('&');
    final Map<String, dynamic> result = {};
    for (var key in keyList) {
      result[key] = getValueWithKey(key);
    }
    return result;
  }

  static dynamic getValueWithKey(String value) {
    final result;
    switch (value) {
      case "name":
        result = name;
        break;
      case "mobile":
        result = mobile;
        break;
      case "id_no":
        result = id_no;
        break;
      case "departmentType":
        result = departmentType;
        break;
      case "departmentName":
        result = departmentName;
        break;
      case "avatar":
        result = avatar;
        break;
      case "token":
        result = token;
        break;
      case "guid":
        result = guid;
        break;
      case "departmentId":
        result = departmentId;
        break;
      case "userId":
        result = userId;
        break;
      case "userRoleNames":
        result = userRoleNames;
        break;
      case "userRoleIds":
        result = userRoleIds;
        break;
      case "timeStamp":
        result = timeStamp;
        break;
      case "school":
        result = schoolInfo;
        break;
      default:
        result = null;
    }
    return result;
  }
}
