# 缓存架构设计文档

## 概述
将项目从MMKV迁移到flutter_cache_manager，采用分层缓存策略，满足不同数据类型的缓存需求。

## 缓存分类策略

### 1. 持久化配置数据 (SharedPreferences)
**用途**: 用户设置、应用配置、登录状态等需要持久化的小数据
**存储方案**: SharedPreferences (Flutter端) + SharedPreferences (Android端)
**过期策略**: 永不过期，手动清理
**数据类型**:
- 用户登录信息 (手机号、姓名、Token、登录状态)
- 应用设置 (推送开关、振动设置、背景播放等)
- 首次使用标记 (各种引导页、弹窗状态)
- 用户偏好设置 (PDF背景色、下载路径等)

### 2. 网络数据缓存 (flutter_cache_manager)
**用途**: API响应数据、图片、文件等网络资源
**存储方案**: flutter_cache_manager
**过期策略**: 基于HTTP Cache-Control头或自定义TTL
**数据类型**:
- API响应数据 (通讯录、菜单列表、圈子分类等)
- 图片资源缓存
- 文件下载缓存

### 3. 临时数据缓存 (flutter_cache_manager)
**用途**: 搜索历史、临时状态等可丢失数据
**存储方案**: flutter_cache_manager (短期缓存)
**过期策略**: 7-30天自动清理
**数据类型**:
- 搜索历史记录
- 临时UI状态
- 学习进度记录

## 缓存管理器设计

### 1. AppCacheManager (主缓存管理器)
```dart
class AppCacheManager {
  // 网络数据缓存 - 7天过期
  static CacheManager networkCache = CacheManager(
    Config(
      'network_cache',
      stalePeriod: Duration(days: 7),
      maxNrOfCacheObjects: 1000,
    ),
  );
  
  // 临时数据缓存 - 30天过期
  static CacheManager tempCache = CacheManager(
    Config(
      'temp_cache', 
      stalePeriod: Duration(days: 30),
      maxNrOfCacheObjects: 500,
    ),
  );
  
  // 图片缓存 - 30天过期
  static CacheManager imageCache = CacheManager(
    Config(
      'image_cache',
      stalePeriod: Duration(days: 30), 
      maxNrOfCacheObjects: 2000,
    ),
  );
}
```

### 2. PreferencesManager (配置管理器)
继续使用现有的SharedPreferences方案，用于持久化配置数据。

## 迁移映射表

### Flutter端迁移
| 原MMKV用途 | 新方案 | 缓存管理器 |
|-----------|--------|-----------|
| 通讯录数据 | JSON缓存 | networkCache |
| API响应缓存 | HTTP缓存 | networkCache |

### Android端迁移  
| 原MMKV数据 | 新方案 | 存储位置 |
|-----------|--------|----------|
| 用户登录信息 | SharedPreferences | Android原生 |
| 应用设置开关 | SharedPreferences | Android原生 |
| 搜索历史 | JSON缓存 | tempCache |
| 学习记录 | SharedPreferences | Android原生 |
| UI状态标记 | SharedPreferences | Android原生 |

## 缓存策略

### 网络数据缓存策略
1. **优先级**: 缓存优先，网络更新
2. **更新机制**: 后台更新，前台展示缓存
3. **过期处理**: 7天后自动清理
4. **容量限制**: 最多1000个对象

### 临时数据缓存策略  
1. **优先级**: 内存优先，磁盘备份
2. **更新机制**: 实时更新
3. **过期处理**: 30天后自动清理
4. **容量限制**: 最多500个对象

### 配置数据策略
1. **持久化**: 永久保存，手动清理
2. **同步机制**: 立即写入
3. **备份**: 跟随应用数据备份

## 性能优化

### 1. 内存管理
- 使用LRU算法管理内存缓存
- 设置合理的内存缓存大小限制
- 及时释放不需要的缓存对象

### 2. 磁盘管理
- 定期清理过期缓存文件
- 监控磁盘使用情况
- 压缩存储大文件

### 3. 网络优化
- 支持HTTP缓存头
- 实现增量更新
- 支持离线模式

## 实施计划

### 阶段1: 基础架构
1. 添加flutter_cache_manager依赖
2. 创建缓存管理器类
3. 实现基础缓存接口

### 阶段2: 数据迁移
1. 迁移Flutter端LocalCacheManager
2. 迁移Android端MMKV扩展
3. 更新网络缓存逻辑

### 阶段3: 清理优化
1. 删除MMKV相关代码
2. 性能测试和优化
3. 文档更新

## 注意事项

1. **数据兼容性**: 确保迁移过程中数据不丢失
2. **性能影响**: 监控缓存性能，避免影响用户体验
3. **存储空间**: 合理设置缓存大小，避免占用过多存储空间
4. **错误处理**: 完善缓存失败的降级方案
